package utils

import (
	"errors"
	"fmt"
)

// Error 自定义错误类型，对应Java中的ServiceException
type Error struct {
	message string
}

// NewError 创建一个新的错误
func NewError(message string) error {
	return errors.New(message)
}

// Error 实现error接口
func (e *Error) Error() string {
	return e.message
}

// WithMessage 使用新的消息创建一个Error
func WithMessage(err error, message string) error {
	if err == nil {
		return nil
	}
	return NewError(fmt.Sprintf("%s: %s", message, err.Error()))
}
