package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysUserRoleRepository 用户与角色关联表Repository接口
type SysUserRoleRepository interface {
	Repository
	// DeleteUserRoleByUserId 通过用户ID删除用户和角色关联
	DeleteUserRoleByUserId(userId int64) error
	// DeleteUserRole 批量删除用户和角色关联
	DeleteUserRole(ids []int64) error
	// CountUserRoleByRoleId 通过角色ID查询角色使用数量
	CountUserRoleByRoleId(roleId int64) (int64, error)
	// BatchUserRole 批量新增用户角色信息
	BatchUserRole(userRoleList []*model.SysUserRole) error
	// DeleteUserRoleInfo 删除用户和角色关联信息
	DeleteUserRoleInfo(userRole *model.SysUserRole) error
	// DeleteUserRoleInfos 批量取消授权用户角色
	DeleteUserRoleInfos(roleId int64, userIds []int64) error
	// InsertUserRole 新增用户角色信息
	InsertUserRole(userId int64, roleIds []int64) error
}

// SysUserRoleRepositoryImpl 用户与角色关联表Repository实现
type SysUserRoleRepositoryImpl struct {
	*BaseRepository
}

// NewSysUserRoleRepository 创建用户与角色关联表Repository
func NewSysUserRoleRepository(db *gorm.DB) SysUserRoleRepository {
	return &SysUserRoleRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// DeleteUserRoleByUserId 通过用户ID删除用户和角色关联
func (r *SysUserRoleRepositoryImpl) DeleteUserRoleByUserId(userId int64) error {
	return r.DB.Where("user_id = ?", userId).Delete(&model.SysUserRole{}).Error
}

// DeleteUserRole 批量删除用户和角色关联
func (r *SysUserRoleRepositoryImpl) DeleteUserRole(ids []int64) error {
	return r.DB.Where("user_id IN ?", ids).Delete(&model.SysUserRole{}).Error
}

// CountUserRoleByRoleId 通过角色ID查询角色使用数量
func (r *SysUserRoleRepositoryImpl) CountUserRoleByRoleId(roleId int64) (int64, error) {
	var count int64
	err := r.DB.Model(&model.SysUserRole{}).Where("role_id = ?", roleId).Count(&count).Error
	return count, err
}

// BatchUserRole 批量新增用户角色信息
func (r *SysUserRoleRepositoryImpl) BatchUserRole(userRoleList []*model.SysUserRole) error {
	if len(userRoleList) == 0 {
		return nil
	}
	return r.DB.Create(&userRoleList).Error
}

// DeleteUserRoleInfo 删除用户和角色关联信息
func (r *SysUserRoleRepositoryImpl) DeleteUserRoleInfo(userRole *model.SysUserRole) error {
	return r.DB.Where("user_id = ? AND role_id = ?", userRole.UserID, userRole.RoleID).Delete(&model.SysUserRole{}).Error
}

// DeleteUserRoleInfos 批量取消授权用户角色
func (r *SysUserRoleRepositoryImpl) DeleteUserRoleInfos(roleId int64, userIds []int64) error {
	return r.DB.Where("role_id = ? AND user_id IN ?", roleId, userIds).Delete(&model.SysUserRole{}).Error
}

// InsertUserRole 新增用户角色信息
func (r *SysUserRoleRepositoryImpl) InsertUserRole(userId int64, roleIds []int64) error {
	if len(roleIds) == 0 {
		return nil
	}

	// 构建用户角色关联列表
	userRoleList := make([]*model.SysUserRole, 0, len(roleIds))
	for _, roleId := range roleIds {
		userRoleList = append(userRoleList, &model.SysUserRole{
			UserID: userId,
			RoleID: roleId,
		})
	}

	// 批量创建用户角色关联
	return r.BatchUserRole(userRoleList)
}
