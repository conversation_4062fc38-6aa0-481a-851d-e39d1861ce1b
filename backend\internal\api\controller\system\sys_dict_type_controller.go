package system

import (
	"backend/internal/api/controller"
	"backend/internal/model"
	"backend/internal/service"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysDictTypeController 字典类型控制器
type SysDictTypeController struct {
	controller.BaseController
	dictTypeService service.SysDictTypeService
}

// NewSysDictTypeController 创建字典类型控制器
func NewSysDictTypeController(dictTypeService service.SysDictTypeService) *SysDictTypeController {
	return &SysDictTypeController{
		dictTypeService: dictTypeService,
	}
}

// List 获取字典类型列表
// @Router /system/dict/type/list [get]
func (c *SysDictTypeController) List(ctx *gin.Context) {
	var dictType model.SysDictType

	// 获取查询参数
	dictName := ctx.Query("dictName")
	dictType.DictName = dictName

	status := ctx.Query("status")
	dictType.Status = status

	dictTypeStr := ctx.Query("dictType")
	dictType.DictType = dictTypeStr

	// 获取分页参数
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	// 查询字典类型列表
	list, err := c.dictTypeService.SelectDictTypeList(&dictType)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// TODO: 实现分页
	// 返回数据
	c.Success(ctx, map[string]interface{}{
		"rows":     list,
		"total":    len(list),
		"pageNum":  pageNum,
		"pageSize": pageSize,
	})
}

// GetInfo 查询字典类型详细
// @Router /system/dict/type/{dictId} [get]
func (c *SysDictTypeController) GetInfo(ctx *gin.Context) {
	dictIdStr := ctx.Param("dictId")
	dictId, err := strconv.ParseInt(dictIdStr, 10, 64)
	if err != nil {
		c.Warn(ctx, "参数错误")
		return
	}

	dictType, err := c.dictTypeService.SelectDictTypeById(dictId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, dictType)
}

// Add 新增字典类型
// @Router /system/dict/type [post]
func (c *SysDictTypeController) Add(ctx *gin.Context) {
	var dict model.SysDictType
	if err := ctx.ShouldBindJSON(&dict); err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验字典类型是否唯一
	if !c.dictTypeService.CheckDictTypeUnique(&dict) {
		c.Warn(ctx, "新增字典'"+dict.DictName+"'失败，字典类型已存在")
		return
	}

	// 设置创建者
	dict.CreateBy = c.GetUsername(ctx)

	dictId, err := c.dictTypeService.InsertDictType(&dict)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, dictId, nil)
}

// Edit 修改字典类型
// @Router /system/dict/type [put]
func (c *SysDictTypeController) Edit(ctx *gin.Context) {
	var dict model.SysDictType
	if err := ctx.ShouldBindJSON(&dict); err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验字典类型是否唯一
	if !c.dictTypeService.CheckDictTypeUnique(&dict) {
		c.Warn(ctx, "修改字典'"+dict.DictName+"'失败，字典类型已存在")
		return
	}

	// 设置更新者
	dict.UpdateBy = c.GetUsername(ctx)

	dictId, err := c.dictTypeService.UpdateDictType(&dict)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, dictId, nil)
}

// Remove 删除字典类型
// @Router /system/dict/type/{dictIds} [delete]
func (c *SysDictTypeController) Remove(ctx *gin.Context) {
	dictIdsStr := ctx.Param("dictIds")
	dictIds := make([]int64, 0)

	for _, idStr := range strings.Split(dictIdsStr, ",") {
		if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
			dictIds = append(dictIds, id)
		}
	}

	err := c.dictTypeService.DeleteDictTypeByIds(dictIds)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// RefreshCache 刷新字典缓存
// @Router /system/dict/type/refreshCache [delete]
func (c *SysDictTypeController) RefreshCache(ctx *gin.Context) {
	err := c.dictTypeService.ResetDictCache()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// OptionSelect 获取字典选择框列表
// @Router /system/dict/type/optionselect [get]
func (c *SysDictTypeController) OptionSelect(ctx *gin.Context) {
	dictTypes, err := c.dictTypeService.SelectDictTypeAll()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, dictTypes)
}

// RegisterRoutes 注册路由
func (c *SysDictTypeController) RegisterRoutes(router *gin.RouterGroup) {
	dictTypeRouter := router.Group("/system/dict/type")
	{
		dictTypeRouter.GET("/list", c.List)
		dictTypeRouter.GET("/:dictId", c.GetInfo)
		dictTypeRouter.POST("", c.Add)
		dictTypeRouter.PUT("", c.Edit)
		dictTypeRouter.DELETE("/:dictIds", c.Remove)
		dictTypeRouter.DELETE("/refreshCache", c.RefreshCache)
		dictTypeRouter.GET("/optionselect", c.OptionSelect)
	}
}
