package impl

import (
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"backend/internal/utils"
	"strconv"
	"strings"
)

// SysDeptServiceImpl 部门服务实现
type SysDeptServiceImpl struct {
	deptRepository repository.SysDeptRepository
	roleRepository repository.SysRoleRepository
}

// NewSysDeptService 创建部门服务实例
func NewSysDeptService(deptRepository repository.SysDeptRepository, roleRepository repository.SysRoleRepository) service.SysDeptService {
	return &SysDeptServiceImpl{
		deptRepository: deptRepository,
		roleRepository: roleRepository,
	}
}

// SelectDeptList 查询部门列表
func (s *SysDeptServiceImpl) SelectDeptList(dept *model.SysDept) ([]*model.SysDept, error) {
	return s.deptRepository.SelectDeptList(dept)
}

// SelectDeptListByRoleId 根据角色ID查询部门树列表
func (s *SysDeptServiceImpl) SelectDeptListByRoleId(roleId int64) ([]int64, error) {
	role, err := s.roleRepository.SelectRoleById(roleId)
	if err != nil {
		return nil, err
	}

	return s.deptRepository.SelectDeptListByRoleId(roleId, role.DeptCheckStrictly)
}

// SelectDeptTreeList 查询部门树结构
func (s *SysDeptServiceImpl) SelectDeptTreeList(dept *model.SysDept) ([]*model.SysDept, error) {
	depts, err := s.deptRepository.SelectDeptList(dept)
	if err != nil {
		return nil, err
	}

	return s.buildDeptTree(depts), nil
}

// SelectDeptById 查询部门
func (s *SysDeptServiceImpl) SelectDeptById(deptId int64) (*model.SysDept, error) {
	dept, err := s.deptRepository.SelectDeptById(deptId)
	if err != nil {
		return nil, err
	}

	if dept != nil && dept.ParentID > 0 {
		parent, err := s.deptRepository.SelectDeptById(dept.ParentID)
		if err != nil {
			return nil, err
		}
		dept.ParentName = parent.DeptName
	}

	return dept, nil
}

// InsertDept 新增部门
func (s *SysDeptServiceImpl) InsertDept(dept *model.SysDept) (int, error) {
	if dept.ParentID != 0 {
		parent, err := s.deptRepository.SelectDeptById(dept.ParentID)
		if err != nil {
			return 0, err
		}

		// 如果父节点不为"正常"状态，则不允许新增子节点
		if parent.Status != "0" {
			return 0, utils.NewError("部门停用，不允许新增")
		}

		// 设置祖级列表
		if parent.Ancestors != "" {
			dept.Ancestors = parent.Ancestors + "," + strconv.FormatInt(dept.ParentID, 10)
		} else {
			dept.Ancestors = strconv.FormatInt(dept.ParentID, 10)
		}
	} else {
		dept.Ancestors = "0"
	}

	_, err := s.deptRepository.InsertDept(dept)
	if err != nil {
		return 0, err
	}

	return 1, nil
}

// UpdateDept 修改部门
func (s *SysDeptServiceImpl) UpdateDept(dept *model.SysDept) (int, error) {
	// 查询现有部门
	oldDept, err := s.deptRepository.SelectDeptById(dept.DeptID)
	if err != nil {
		return 0, err
	}

	// 如果父节点不为正常状态，则不允许修改
	if dept.ParentID != 0 {
		parent, err := s.deptRepository.SelectDeptById(dept.ParentID)
		if err != nil {
			return 0, err
		}
		if parent.Status != "0" {
			return 0, utils.NewError("部门停用，不允许修改")
		}

		// 判断是否修改了父级ID，如果修改了则需要更新祖级列表
		if oldDept.ParentID != dept.ParentID {
			// 设置新的祖级列表
			if parent.Ancestors != "" {
				dept.Ancestors = parent.Ancestors + "," + strconv.FormatInt(dept.ParentID, 10)
			} else {
				dept.Ancestors = strconv.FormatInt(dept.ParentID, 10)
			}

			// 获取所有子部门并更新祖级列表
			children, err := s.deptRepository.SelectChildrenDeptById(dept.DeptID)
			if err != nil {
				return 0, err
			}

			if len(children) > 0 {
				for _, child := range children {
					// 更新子部门的祖级列表
					newAncestors := strings.Replace(child.Ancestors, oldDept.Ancestors, dept.Ancestors, 1)
					child.Ancestors = newAncestors
					// 更新子部门
					err = s.deptRepository.UpdateDept(child)
					if err != nil {
						return 0, err
					}
				}
			}
		}
	} else {
		dept.Ancestors = "0"
	}

	// 更新部门信息
	err = s.deptRepository.UpdateDept(dept)
	if err != nil {
		return 0, err
	}

	return 1, nil
}

// DeleteDeptById 删除部门
func (s *SysDeptServiceImpl) DeleteDeptById(deptId int64) (int, error) {
	// 检查是否存在子部门
	hasChild, err := s.HasChildByDeptId(deptId)
	if err != nil {
		return 0, err
	}
	if hasChild {
		return 0, utils.NewError("存在下级部门，不允许删除")
	}

	// 检查是否存在用户
	existUser, err := s.CheckDeptExistUser(deptId)
	if err != nil {
		return 0, err
	}
	if existUser {
		return 0, utils.NewError("部门存在用户，不允许删除")
	}

	// 删除部门
	err = s.deptRepository.DeleteDeptById(deptId)
	if err != nil {
		return 0, err
	}

	return 1, nil
}

// HasChildByDeptId 是否存在子部门
func (s *SysDeptServiceImpl) HasChildByDeptId(deptId int64) (bool, error) {
	return s.deptRepository.HasChildByDeptId(deptId)
}

// CheckDeptExistUser 查询部门是否存在用户
func (s *SysDeptServiceImpl) CheckDeptExistUser(deptId int64) (bool, error) {
	return s.deptRepository.CheckDeptExistUser(deptId)
}

// CheckDeptNameUnique 校验部门名称是否唯一
func (s *SysDeptServiceImpl) CheckDeptNameUnique(dept *model.SysDept) (bool, error) {
	return s.deptRepository.CheckDeptNameUnique(dept)
}

// SelectChildrenDeptById 根据ID查询所有子部门
func (s *SysDeptServiceImpl) SelectChildrenDeptById(deptId int64) ([]*model.SysDept, error) {
	return s.deptRepository.SelectChildrenDeptById(deptId)
}

// SelectNormalChildrenDeptById 根据ID查询所有正常状态的子部门
func (s *SysDeptServiceImpl) SelectNormalChildrenDeptById(deptId int64) (int64, error) {
	// 查询所有子部门
	children, err := s.deptRepository.SelectChildrenDeptById(deptId)
	if err != nil {
		return 0, err
	}

	// 统计正常状态的子部门数量
	var count int64
	for _, child := range children {
		if child.Status == "0" { // 正常状态
			count++
		}
	}

	return count, nil
}

// CheckDeptDataScope 校验部门是否有数据权限
func (s *SysDeptServiceImpl) CheckDeptDataScope(deptId int64) error {
	// 暂时不实现数据权限检查，返回nil表示有权限
	return nil
}

// buildDeptTree 构建部门树
func (s *SysDeptServiceImpl) buildDeptTree(depts []*model.SysDept) []*model.SysDept {
	// 创建部门ID到部门的映射
	deptMap := make(map[int64]*model.SysDept)
	for _, dept := range depts {
		deptMap[dept.DeptID] = dept
	}

	// 根节点集合
	rootDepts := make([]*model.SysDept, 0)

	// 遍历部门
	for _, dept := range depts {
		if parent, exists := deptMap[dept.ParentID]; exists {
			// 找到父节点，作为子节点添加
			if parent.Children == nil {
				parent.Children = make([]*model.SysDept, 0)
			}
			parent.Children = append(parent.Children, dept)
		} else {
			// 未找到父节点，可能是根节点
			rootDepts = append(rootDepts, dept)
		}
	}

	// 如果没有找到根节点，则直接返回所有部门
	if len(rootDepts) == 0 {
		return depts
	}

	return rootDepts
}
