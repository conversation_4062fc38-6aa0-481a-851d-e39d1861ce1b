package middleware

import (
	"time"

	"github.com/gin-gonic/gin"
)

// Cors 跨域配置
func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON>er("Access-Control-Allow-Methods", "GET,POST,PUT,PATCH,DELETE,OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "authorization, origin, content-type, accept")
		c.<PERSON>er("Allow", "HEAD,GET,POST,PUT,PATCH,DELETE,OPTIONS")
		c.<PERSON>er("Content-Type", "application/json")

		// 放行所有OPTIONS方法
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(200)
			return
		}

		// 处理请求
		c.Next()
	}
}

// JWTAuth JWT认证中间件
func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取token
		token := c.Request.Header.Get("Authorization")

		if token == "" {
			c.J<PERSON>(200, gin.H{
				"code": 401,
				"msg":  "请求未授权",
			})
			c.Abort()
			return
		}

		// TODO: 实现JWT认证逻辑

		c.Next()
	}
}

// RateLimiter 限流中间件
func RateLimiter() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: 实现限流逻辑
		c.Next()
	}
}

// Logger 日志中间件
func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开始时间
		startTime := time.Now()

		// 处理请求
		c.Next()

		// 结束时间
		endTime := time.Now()

		// 执行时间
		latencyTime := endTime.Sub(startTime)

		// 请求方式
		reqMethod := c.Request.Method

		// 请求路由
		reqUri := c.Request.RequestURI

		// 状态码
		statusCode := c.Writer.Status()

		// 请求IP
		clientIP := c.ClientIP()

		// 日志格式
		// TODO: 替换为正式的日志记录
		_ = statusCode
		_ = reqMethod
		_ = reqUri
		_ = clientIP
		_ = latencyTime
	}
}
