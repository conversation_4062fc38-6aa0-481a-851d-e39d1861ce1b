package middleware

import (
	"backend/internal/constants"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"bytes"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// OperLogWriter 自定义响应体写入器，用于记录响应数据
type OperLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// Write 重写Write方法，用于记录响应数据
func (w *OperLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// OperLog 操作日志中间件
func OperLog(operLogService service.SysOperLogService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过GET请求的日志记录
		if c.Request.Method == http.MethodGet {
			c.Next()
			return
		}

		// 获取请求开始时间
		startTime := time.Now()

		// 获取请求参数
		var requestBody []byte
		if c.Request.Body != nil {
			var err error
			requestBody, err = io.ReadAll(c.Request.Body)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "读取请求体失败"})
				c.Abort()
				return
			}
			// 重新设置请求体，以便后续中间件和控制器能够读取
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 使用自定义响应写入器记录响应数据
		writer := &OperLogWriter{
			ResponseWriter: c.Writer,
			body:           &bytes.Buffer{},
		}
		c.Writer = writer

		// 执行请求
		c.Next()

		// 记录日志
		// 判断是否需要跳过日志记录（如登录页面等）
		if shouldSkipLog(c.Request.URL.Path) {
			return
		}

		// 获取当前登录用户
		userObj, exists := c.Get(constants.LOGIN_USER_KEY)
		var username string
		if !exists {
			username = "未登录用户"
		} else {
			if loginUser, ok := userObj.(*model.LoginUser); ok {
				username = utils.GetUsernameFromUser(loginUser)
			} else {
				username = "未知用户"
			}
		}

		// 获取操作人IP
		clientIP := c.ClientIP()

		// 构建操作日志
		operLog := &model.SysOperLog{
			Title:         getOperTitle(c.Request.URL.Path),            // 模块标题
			BusinessType:  getBusinessType(c.Request.Method),           // 业务类型
			Method:        c.Request.URL.Path,                          // 请求方法
			RequestMethod: c.Request.Method,                            // HTTP请求方式
			OperatorType:  1,                                           // 操作类别
			OperName:      username,                                    // 操作人员
			OperIp:        clientIP,                                    // 主机地址
			OperLocation:  utils.GetLocationByIP(clientIP),             // 操作地点
			OperTime:      &time.Time{},                                // 操作时间
			OperParam:     string(requestBody),                         // 请求参数
			JsonResult:    writer.body.String(),                        // 返回参数
			Status:        0,                                           // 操作状态（0正常 1异常）
			ErrorMsg:      "",                                          // 错误消息
			OperUrl:       c.Request.URL.Path,                          // 请求URL
			CostTime:      int64(time.Since(startTime).Milliseconds()), // 消耗时间
		}

		// 设置操作时间
		operLog.OperTime = &startTime

		// 异步记录日志
		go operLogService.InsertOperLog(operLog)
	}
}

// shouldSkipLog 判断是否跳过日志记录
func shouldSkipLog(path string) bool {
	// 跳过登录、验证码等接口的日志记录
	skipPaths := []string{
		"/login",
		"/captchaImage",
		"/getInfo",
		"/getRouters",
	}

	for _, skipPath := range skipPaths {
		if strings.Contains(path, skipPath) {
			return true
		}
	}
	return false
}

// getOperTitle 获取模块标题
func getOperTitle(path string) string {
	// 根据URL路径判断模块
	if strings.Contains(path, "/system/user") {
		return "用户管理"
	} else if strings.Contains(path, "/system/role") {
		return "角色管理"
	} else if strings.Contains(path, "/system/menu") {
		return "菜单管理"
	} else if strings.Contains(path, "/system/dept") {
		return "部门管理"
	} else if strings.Contains(path, "/system/post") {
		return "岗位管理"
	} else if strings.Contains(path, "/system/dict") {
		return "字典管理"
	} else if strings.Contains(path, "/system/config") {
		return "参数管理"
	} else if strings.Contains(path, "/system/notice") {
		return "通知公告"
	}
	return "其他"
}

// getBusinessType 获取业务类型
func getBusinessType(method string) int {
	switch method {
	case http.MethodPost:
		return 1 // 新增
	case http.MethodPut:
		return 2 // 修改
	case http.MethodDelete:
		return 3 // 删除
	case http.MethodGet:
		return 0 // 其他
	default:
		return 0 // 其他
	}
}
