package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysJobLogRepository 定时任务日志数据访问接口
type SysJobLogRepository interface {
	// SelectJobLogList 查询任务日志列表
	SelectJobLogList(jobLog *model.SysJobLog) ([]*model.SysJobLog, error)

	// SelectJobLogById 通过ID查询任务日志
	SelectJobLogById(jobLogId int64) (*model.SysJobLog, error)

	// InsertJobLog 新增任务日志
	InsertJobLog(jobLog *model.SysJobLog) error

	// DeleteJobLogById 删除任务日志
	DeleteJobLogById(jobLogId int64) (int, error)

	// DeleteJobLogByIds 批量删除任务日志
	DeleteJobLogByIds(jobLogIds []int64) (int, error)

	// CleanJobLog 清空任务日志
	CleanJobLog() error
}

// NewSysJobLogRepository 创建任务日志仓库实例
func NewSysJobLogRepository(db *gorm.DB) SysJobLogRepository {
	return &sysJobLogRepository{db: db}
}

// sysJobLogRepository 任务日志仓库实现
type sysJobLogRepository struct {
	db *gorm.DB
}

// SelectJobLogList 查询任务日志列表
func (r *sysJobLogRepository) SelectJobLogList(jobLog *model.SysJobLog) ([]*model.SysJobLog, error) {
	var jobLogs []*model.SysJobLog
	db := r.db.Model(&model.SysJobLog{})

	if jobLog.JobName != "" {
		db = db.Where("job_name like ?", "%"+jobLog.JobName+"%")
	}

	if jobLog.JobGroup != "" {
		db = db.Where("job_group = ?", jobLog.JobGroup)
	}

	if jobLog.Status != "" {
		db = db.Where("status = ?", jobLog.Status)
	}

	if jobLog.InvokeTarget != "" {
		db = db.Where("invoke_target like ?", "%"+jobLog.InvokeTarget+"%")
	}

	// 按开始时间倒序排列
	err := db.Order("start_time DESC").Find(&jobLogs).Error
	return jobLogs, err
}

// SelectJobLogById 通过ID查询任务日志
func (r *sysJobLogRepository) SelectJobLogById(jobLogId int64) (*model.SysJobLog, error) {
	var jobLog model.SysJobLog
	err := r.db.Where("job_log_id = ?", jobLogId).First(&jobLog).Error
	return &jobLog, err
}

// InsertJobLog 新增任务日志
func (r *sysJobLogRepository) InsertJobLog(jobLog *model.SysJobLog) error {
	return r.db.Create(jobLog).Error
}

// DeleteJobLogById 删除任务日志
func (r *sysJobLogRepository) DeleteJobLogById(jobLogId int64) (int, error) {
	result := r.db.Delete(&model.SysJobLog{}, jobLogId)
	return int(result.RowsAffected), result.Error
}

// DeleteJobLogByIds 批量删除任务日志
func (r *sysJobLogRepository) DeleteJobLogByIds(jobLogIds []int64) (int, error) {
	result := r.db.Delete(&model.SysJobLog{}, jobLogIds)
	return int(result.RowsAffected), result.Error
}

// CleanJobLog 清空任务日志
func (r *sysJobLogRepository) CleanJobLog() error {
	return r.db.Exec("TRUNCATE TABLE sys_job_log").Error
}
