package impl

import (
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"sync"
)

// 字典缓存
var dictCache sync.Map

// SysDictTypeServiceImpl 字典类型服务实现
type SysDictTypeServiceImpl struct {
	dictTypeRepo repository.SysDictTypeRepository
	dictDataRepo repository.SysDictDataRepository
}

// NewSysDictTypeService 创建字典类型服务
func NewSysDictTypeService(dictTypeRepo repository.SysDictTypeRepository, dictDataRepo repository.SysDictDataRepository) service.SysDictTypeService {
	return &SysDictTypeServiceImpl{
		dictTypeRepo: dictTypeRepo,
		dictDataRepo: dictDataRepo,
	}
}

// SelectDictTypeList 查询字典类型列表
func (s *SysDictTypeServiceImpl) SelectDictTypeList(dictType *model.SysDictType) ([]*model.SysDictType, error) {
	return s.dictTypeRepo.SelectDictTypeList(dictType)
}

// SelectDictTypeAll 查询所有字典类型
func (s *SysDictTypeServiceImpl) SelectDictTypeAll() ([]*model.SysDictType, error) {
	return s.dictTypeRepo.SelectDictTypeAll()
}

// SelectDictTypeById 根据字典类型ID查询信息
func (s *SysDictTypeServiceImpl) SelectDictTypeById(dictId int64) (*model.SysDictType, error) {
	return s.dictTypeRepo.SelectDictTypeById(dictId)
}

// SelectDictTypeByType 根据字典类型查询信息
func (s *SysDictTypeServiceImpl) SelectDictTypeByType(dictType string) (*model.SysDictType, error) {
	return s.dictTypeRepo.SelectDictTypeByType(dictType)
}

// SelectDictDataByType 根据字典类型查询字典数据
func (s *SysDictTypeServiceImpl) SelectDictDataByType(dictType string) ([]*model.SysDictData, error) {
	// 先从缓存中获取
	if value, ok := dictCache.Load(dictType); ok {
		return value.([]*model.SysDictData), nil
	}

	// 缓存中没有，从数据库查询
	dictDataList, err := s.dictDataRepo.SelectDictDataByType(dictType)
	if err != nil {
		return nil, err
	}

	// 放入缓存
	dictCache.Store(dictType, dictDataList)

	return dictDataList, nil
}

// DeleteDictTypeById 删除字典类型
func (s *SysDictTypeServiceImpl) DeleteDictTypeById(dictId int64) error {
	dictType, err := s.SelectDictTypeById(dictId)
	if err != nil {
		return err
	}

	// 删除字典类型
	err = s.dictTypeRepo.DeleteDictTypeById(dictId)
	if err != nil {
		return err
	}

	// 删除缓存
	dictCache.Delete(dictType.DictType)

	return nil
}

// DeleteDictTypeByIds 批量删除字典类型
func (s *SysDictTypeServiceImpl) DeleteDictTypeByIds(dictIds []int64) error {
	// 先查询所有要删除的字典类型
	for _, dictId := range dictIds {
		dictType, err := s.SelectDictTypeById(dictId)
		if err == nil && dictType != nil {
			// 检查字典类型是否有字典数据
			count, err := s.dictDataRepo.CountDictDataByType(dictType.DictType)
			if err != nil {
				return err
			}
			if count > 0 {
				return service.NewError("字典'" + dictType.DictName + "'已分配，不能删除")
			}

			// 删除缓存
			dictCache.Delete(dictType.DictType)
		}
	}

	// 批量删除字典类型
	return s.dictTypeRepo.DeleteDictTypeByIds(dictIds)
}

// InsertDictType 新增字典类型
func (s *SysDictTypeServiceImpl) InsertDictType(dictType *model.SysDictType) (int64, error) {
	return s.dictTypeRepo.InsertDictType(dictType)
}

// UpdateDictType 修改字典类型
func (s *SysDictTypeServiceImpl) UpdateDictType(dictType *model.SysDictType) (int64, error) {
	oldDict, err := s.dictTypeRepo.SelectDictTypeById(dictType.DictID)
	if err != nil {
		return 0, err
	}

	// 更新字典类型
	dictId, err := s.dictTypeRepo.UpdateDictType(dictType)
	if err != nil {
		return 0, err
	}

	// 如果字典类型发生变化，需要更新字典数据的字典类型
	if oldDict.DictType != dictType.DictType {
		// TODO: 修改字典数据表中的字典类型

		// 删除旧缓存
		dictCache.Delete(oldDict.DictType)
	}

	return dictId, nil
}

// CheckDictTypeUnique 校验字典类型是否唯一
func (s *SysDictTypeServiceImpl) CheckDictTypeUnique(dictType *model.SysDictType) bool {
	// 查询是否存在相同类型的字典
	existDict, err := s.dictTypeRepo.CheckDictTypeUnique(dictType.DictType, dictType.DictID)
	if err != nil {
		return false
	}

	// 没有找到相同类型的字典，表示唯一
	return existDict == nil
}

// ResetDictCache 重置字典缓存数据
func (s *SysDictTypeServiceImpl) ResetDictCache() error {
	// 清空缓存
	err := s.ClearDictCache()
	if err != nil {
		return err
	}

	// 加载缓存
	return s.LoadingDictCache()
}

// LoadingDictCache 加载字典缓存数据
func (s *SysDictTypeServiceImpl) LoadingDictCache() error {
	// 查询所有字典类型
	dictTypeList, err := s.dictTypeRepo.SelectDictTypeAll()
	if err != nil {
		return err
	}

	// 遍历字典类型，加载字典数据到缓存
	for _, dictType := range dictTypeList {
		dictDataList, err := s.dictDataRepo.SelectDictDataByType(dictType.DictType)
		if err == nil {
			dictCache.Store(dictType.DictType, dictDataList)
		}
	}

	return nil
}

// ClearDictCache 清空字典缓存数据
func (s *SysDictTypeServiceImpl) ClearDictCache() error {
	dictCache = sync.Map{}
	return nil
}
