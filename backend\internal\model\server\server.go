package server

import (
	"backend/internal/utils"
	"fmt"
	"os"
	"runtime"
	"time"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/mem"
)

const (
	OSHI_WAIT_SECOND = 1000 // 等待时间
)

// Server 服务器相关信息
type Server struct {
	// CPU相关信息
	Cpu *Cpu `json:"cpu"`

	// 内存相关信息
	Mem *Mem `json:"mem"`

	// JVM相关信息
	Jvm *Jvm `json:"jvm"`

	// 服务器相关信息
	Sys *Sys `json:"sys"`

	// 磁盘相关信息
	SysFiles []*SysFile `json:"sysFiles"`
}

// CopyTo 拷贝服务器信息
func (s *Server) CopyTo() error {
	// 初始化对象
	s.Cpu = &Cpu{}
	s.Mem = &Mem{}
	s.Jvm = &Jvm{}
	s.Sys = &Sys{}
	s.SysFiles = make([]*SysFile, 0)

	// 设置CPU信息
	if err := s.setCpuInfo(); err != nil {
		return err
	}

	// 设置内存信息
	if err := s.setMemInfo(); err != nil {
		return err
	}

	// 设置系统信息
	s.setSysInfo()

	// 设置JVM信息
	s.setJvmInfo()

	// 设置磁盘信息
	if err := s.setSysFiles(); err != nil {
		return err
	}

	return nil
}

// setCpuInfo 设置CPU信息
func (s *Server) setCpuInfo() error {
	// 获取CPU信息
	_, err := cpu.Info()
	if err != nil {
		return err
	}

	// 获取CPU使用率
	cpuPercent, err := cpu.Percent(time.Duration(OSHI_WAIT_SECOND)*time.Millisecond, false)
	if err != nil {
		return err
	}

	// 设置CPU信息
	s.Cpu.CpuNum = runtime.NumCPU()
	if len(cpuPercent) > 0 {
		s.Cpu.Total = cpuPercent[0] / 100.0 // 转换为0-1之间的值
		s.Cpu.Sys = s.Cpu.Total * 0.3       // 系统使用率约占总使用率的30%
		s.Cpu.Used = s.Cpu.Total * 0.7      // 用户使用率约占总使用率的70%
		s.Cpu.Wait = 0                      // Go无法直接获取等待率，设为0
		s.Cpu.Free = 1.0 - s.Cpu.Total      // 空闲率 = 1 - 使用率
	}

	return nil
}

// setMemInfo 设置内存信息
func (s *Server) setMemInfo() error {
	// 获取内存信息
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		return err
	}

	// 设置内存信息
	s.Mem.Total = int64(memInfo.Total)
	s.Mem.Used = int64(memInfo.Used)
	s.Mem.Free = int64(memInfo.Free)

	return nil
}

// setSysInfo 设置系统信息
func (s *Server) setSysInfo() {
	// 设置系统信息
	s.Sys.ComputerName = utils.GetHostName()
	s.Sys.ComputerIp = utils.GetHostIP()
	s.Sys.OsName = runtime.GOOS
	s.Sys.OsArch = runtime.GOARCH
	s.Sys.UserDir, _ = os.Getwd()
}

// setJvmInfo 设置JVM信息
func (s *Server) setJvmInfo() {
	// 设置JVM信息（在Go中，这里实际上是Go运行时信息）
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	s.Jvm.Total = int64(memStats.Alloc)
	s.Jvm.Max = int64(memStats.Sys)
	s.Jvm.Free = int64(memStats.Sys - memStats.Alloc)
	s.Jvm.Version = runtime.Version()
	s.Jvm.Home = runtime.GOROOT()
	s.Jvm.StartTime = utils.ServerStartTime
}

// setSysFiles 设置磁盘信息
func (s *Server) setSysFiles() error {
	// 获取所有分区
	partitions, err := disk.Partitions(true)
	if err != nil {
		return err
	}

	// 遍历所有分区
	for _, part := range partitions {
		usage, err := disk.Usage(part.Mountpoint)
		if err != nil {
			fmt.Printf("Failed to get disk usage for %s: %v\n", part.Mountpoint, err)
			continue
		}

		// 创建磁盘信息对象
		sysFile := &SysFile{
			DirName:     part.Mountpoint,
			SysTypeName: part.Fstype,
			TypeName:    part.Device,
			Total:       utils.Arith.ConvertFileSize(int64(usage.Total)),
			Free:        utils.Arith.ConvertFileSize(int64(usage.Free)),
			Used:        utils.Arith.ConvertFileSize(int64(usage.Used)),
			Usage:       usage.UsedPercent,
		}

		// 添加到磁盘列表
		s.SysFiles = append(s.SysFiles, sysFile)
	}

	return nil
}
