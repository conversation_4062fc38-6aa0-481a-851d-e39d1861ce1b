package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ResponseData 通用响应结构
type ResponseData struct {
	Code  int         `json:"code"`  // 响应码
	Msg   string      `json:"msg"`   // 响应消息
	Data  interface{} `json:"data"`  // 响应数据
	Rows  interface{} `json:"rows"`  // 数据行
	Total int64       `json:"total"` // 总数
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, ResponseData{
		Code: http.StatusOK,
		Msg:  "操作成功",
		Data: data,
	})
}

// SuccessWithMessage 带消息的成功响应
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, ResponseData{
		Code: http.StatusOK,
		Msg:  message,
		Data: data,
	})
}

// SuccessWithTable 表格数据响应
func SuccessWithTable(c *gin.Context, rows interface{}, total int64) {
	c.JSON(http.StatusOK, ResponseData{
		Code:  http.StatusOK,
		Msg:   "查询成功",
		Rows:  rows,
		Total: total,
	})
}

// Error 错误响应
func Error(c *gin.Context, message string) {
	c.JSON(http.StatusOK, ResponseData{
		Code: http.StatusInternalServerError,
		Msg:  message,
	})
}

// ErrorWithCode 带状态码的错误响应
func ErrorWithCode(c *gin.Context, code int, message string) {
	c.JSON(http.StatusOK, ResponseData{
		Code: code,
		Msg:  message,
	})
}

// Unauthorized 未授权响应
func Unauthorized(c *gin.Context, message string) {
	c.JSON(http.StatusOK, ResponseData{
		Code: http.StatusUnauthorized,
		Msg:  message,
	})
}

// Forbidden 禁止访问响应
func Forbidden(c *gin.Context, message string) {
	c.JSON(http.StatusOK, ResponseData{
		Code: http.StatusForbidden,
		Msg:  message,
	})
}

// BadRequest 请求参数错误响应
func BadRequest(c *gin.Context, message string) {
	c.JSON(http.StatusOK, ResponseData{
		Code: http.StatusBadRequest,
		Msg:  message,
	})
}
