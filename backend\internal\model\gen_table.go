package model

import (
	"time"
)

// GenTable 代码生成业务表
type GenTable struct {
	// 编号
	TableID int64 `json:"tableId" gorm:"column:table_id;primary_key;auto_increment;comment:编号"`
	// 表名称
	TableName string `json:"tableName" gorm:"column:table_name;not null;comment:表名称"`
	// 表描述
	TableComment string `json:"tableComment" gorm:"column:table_comment;comment:表描述"`
	// 关联父表的表名
	SubTableName string `json:"subTableName" gorm:"column:sub_table_name;comment:关联父表的表名"`
	// 本表关联父表的外键名
	SubTableFkName string `json:"subTableFkName" gorm:"column:sub_table_fk_name;comment:本表关联父表的外键名"`
	// 实体类名称
	ClassName string `json:"className" gorm:"column:class_name;comment:实体类名称"`
	// 使用的模板（crud单表操作 tree树表操作 sub主子表操作）
	TplCategory string `json:"tplCategory" gorm:"column:tpl_category;comment:使用的模板（crud单表操作 tree树表操作 sub主子表操作）"`
	// 前端类型（element-ui模版 element-plus模版）
	TplWebType string `json:"tplWebType" gorm:"column:tpl_web_type;comment:前端类型（element-ui模版 element-plus模版）"`
	// 生成包路径
	PackageName string `json:"packageName" gorm:"column:package_name;comment:生成包路径"`
	// 生成模块名
	ModuleName string `json:"moduleName" gorm:"column:module_name;comment:生成模块名"`
	// 生成业务名
	BusinessName string `json:"businessName" gorm:"column:business_name;comment:生成业务名"`
	// 生成功能名
	FunctionName string `json:"functionName" gorm:"column:function_name;comment:生成功能名"`
	// 生成功能作者
	FunctionAuthor string `json:"functionAuthor" gorm:"column:function_author;comment:生成功能作者"`
	// 生成代码方式（0zip压缩包 1自定义路径）
	GenType string `json:"genType" gorm:"column:gen_type;comment:生成代码方式（0zip压缩包 1自定义路径）"`
	// 生成路径（不填默认项目路径）
	GenPath string `json:"genPath" gorm:"column:gen_path;comment:生成路径（不填默认项目路径）"`
	// 其它生成选项
	Options string `json:"options" gorm:"column:options;comment:其它生成选项"`
	// 创建者
	CreateBy string `json:"createBy" gorm:"column:create_by;comment:创建者"`
	// 创建时间
	CreateTime *time.Time `json:"createTime" gorm:"column:create_time;comment:创建时间"`
	// 更新者
	UpdateBy string `json:"updateBy" gorm:"column:update_by;comment:更新者"`
	// 更新时间
	UpdateTime *time.Time `json:"updateTime" gorm:"column:update_time;comment:更新时间"`
	// 备注
	Remark string `json:"remark" gorm:"column:remark;comment:备注"`

	// 表列信息
	Columns []*GenTableColumn `json:"columns" gorm:"-"`
	// 主键信息
	PkColumn *GenTableColumn `json:"pkColumn" gorm:"-"`
	// 子表信息
	SubTable *GenTable `json:"subTable" gorm:"-"`
	// 树编码字段
	TreeCode string `json:"treeCode" gorm:"-"`
	// 树父编码字段
	TreeParentCode string `json:"treeParentCode" gorm:"-"`
	// 树名称字段
	TreeName string `json:"treeName" gorm:"-"`
	// 上级菜单ID字段
	ParentMenuID int64 `json:"parentMenuId" gorm:"-"`
	// 上级菜单名称字段
	ParentMenuName string `json:"parentMenuName" gorm:"-"`
}

// GetTableName 设置表名
func (GenTable) GetTableName() string {
	return "gen_table"
}

// IsSub 是否是子表
func (g *GenTable) IsSub() bool {
	return g.TplCategory == "sub"
}

// IsTree 是否是树表
func (g *GenTable) IsTree() bool {
	return g.TplCategory == "tree"
}

// IsCrud 是否是单表
func (g *GenTable) IsCrud() bool {
	return g.TplCategory == "crud"
}
