package model

// GenTable 业务表 gen_table
type GenTable struct {
	BaseEntity
	TableId        uint64 `json:"tableId" gorm:"column:table_id;primaryKey;comment:编号"`
	TableNameStr   string `json:"tableName" gorm:"column:table_name;type:varchar(200);comment:表名称"`
	TableComment   string `json:"tableComment" gorm:"column:table_comment;type:varchar(500);comment:表描述"`
	SubTableName   string `json:"subTableName" gorm:"column:sub_table_name;type:varchar(64);comment:关联父表的表名"`
	SubTableFkName string `json:"subTableFkName" gorm:"column:sub_table_fk_name;type:varchar(64);comment:本表关联父表的外键名"`
	ClassName      string `json:"className" gorm:"column:class_name;type:varchar(100);comment:实体类名称(首字母大写)"`
	TplCategory    string `json:"tplCategory" gorm:"column:tpl_category;type:varchar(200);default:crud;comment:使用的模板（crud单表操作 tree树表操作 sub主子表操作）"`
	TplWebType     string `json:"tplWebType" gorm:"column:tpl_web_type;type:varchar(30);comment:前端类型（element-ui模版 element-plus模版）"`
	PackageName    string `json:"packageName" gorm:"column:package_name;type:varchar(100);comment:生成包路径"`
	ModuleName     string `json:"moduleName" gorm:"column:module_name;type:varchar(30);comment:生成模块名"`
	BusinessName   string `json:"businessName" gorm:"column:business_name;type:varchar(30);comment:生成业务名"`
	FunctionName   string `json:"functionName" gorm:"column:function_name;type:varchar(50);comment:生成功能名"`
	FunctionAuthor string `json:"functionAuthor" gorm:"column:function_author;type:varchar(50);comment:生成作者"`
	GenType        string `json:"genType" gorm:"column:gen_type;type:char(1);default:0;comment:生成代码方式（0zip压缩包 1自定义路径）"`
	GenPath        string `json:"genPath" gorm:"column:gen_path;type:varchar(200);default:/;comment:生成路径（不填默认项目路径）"`
	Options        string `json:"options" gorm:"column:options;type:varchar(1000);comment:其它生成选项"`
	TreeCode       string `json:"treeCode" gorm:"column:tree_code;type:varchar(200);comment:树编码字段"`
	TreeParentCode string `json:"treeParentCode" gorm:"column:tree_parent_code;type:varchar(200);comment:树父编码字段"`
	TreeName       string `json:"treeName" gorm:"column:tree_name;type:varchar(200);comment:树名称字段"`
	ParentMenuId   uint64 `json:"parentMenuId" gorm:"column:parent_menu_id;comment:上级菜单ID字段"`
	ParentMenuName string `json:"parentMenuName" gorm:"-;comment:上级菜单名称字段"`

	// 关联字段
	PkColumn *GenTableColumn   `json:"pkColumn" gorm:"-"` // 主键信息
	SubTable *GenTable         `json:"subTable" gorm:"-"` // 子表信息
	Columns  []*GenTableColumn `json:"columns" gorm:"-"`  // 表列信息
}

// IsSub 是否为子表
func (g *GenTable) IsSub() bool {
	return g.TplCategory == "sub"
}

// IsTree 是否为树表
func (g *GenTable) IsTree() bool {
	return g.TplCategory == "tree"
}

// IsCrud 是否为单表
func (g *GenTable) IsCrud() bool {
	return g.TplCategory == "crud"
}

// TableName 返回表名
func (GenTable) TableName() string {
	return "gen_table"
}
