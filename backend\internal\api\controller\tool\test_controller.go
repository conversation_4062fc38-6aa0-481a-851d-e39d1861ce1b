package tool

import (
	"backend/internal/api/controller"
	"backend/internal/model"
	"backend/internal/utils"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// TestController 测试操作处理
type TestController struct {
	controller.BaseController
}

// NewTestController 创建测试控制器
func NewTestController() *TestController {
	return &TestController{}
}

// UserList 获取用户列表
// @Summary 获取用户列表
// @Description 获取用户列表
// @Tags 测试工具
// @Accept json
// @Produce json
// @Param userName query string false "用户名称"
// @Param userSex query string false "用户性别"
// @Success 200 {object} controller.Response "成功"
// @Router /test/user/list [get]
func (c *TestController) UserList(ctx *gin.Context) {
	// 获取查询参数
	userName := ctx.Query("userName")
	userSex := ctx.Query("userSex")

	// 创建模拟用户列表
	users := []model.TestUser{
		{
			ID:         1,
			UserName:   "admin",
			NickName:   "管理员",
			Email:      "<EMAIL>",
			Phone:      "15888888888",
			Sex:        "1",
			Avatar:     "",
			Password:   "",
			Status:     "0",
			DelFlag:    "0",
			LoginIP:    "127.0.0.1",
			LoginDate:  time.Now(),
			CreateBy:   "admin",
			CreateTime: time.Now(),
			UpdateBy:   "admin",
			UpdateTime: time.Now(),
			Remark:     "管理员",
		},
		{
			ID:         2,
			UserName:   "test",
			NickName:   "测试用户",
			Email:      "<EMAIL>",
			Phone:      "15666666666",
			Sex:        "0",
			Avatar:     "",
			Password:   "",
			Status:     "0",
			DelFlag:    "0",
			LoginIP:    "127.0.0.1",
			LoginDate:  time.Now(),
			CreateBy:   "admin",
			CreateTime: time.Now(),
			UpdateBy:   "admin",
			UpdateTime: time.Now(),
			Remark:     "测试用户",
		},
	}

	// 根据查询参数过滤用户
	var filteredUsers []model.TestUser
	for _, user := range users {
		if (userName == "" || utils.ContainsIgnoreCase(user.UserName, userName)) &&
			(userSex == "" || user.Sex == userSex) {
			filteredUsers = append(filteredUsers, user)
		}
	}

	// 返回结果
	c.Success(ctx, filteredUsers)
}

// GetUser 获取用户详细信息
// @Summary 获取用户详细信息
// @Description 获取用户详细信息
// @Tags 测试工具
// @Accept json
// @Produce json
// @Param userId path int true "用户ID"
// @Success 200 {object} controller.Response "成功"
// @Router /test/user/{userId} [get]
func (c *TestController) GetUser(ctx *gin.Context) {
	// 获取用户ID
	userIDStr := ctx.Param("userId")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		c.Error(ctx, fmt.Errorf("用户ID无效"))
		return
	}

	// 模拟根据ID查询用户
	var user *model.TestUser
	if userID == 1 {
		user = &model.TestUser{
			ID:         1,
			UserName:   "admin",
			NickName:   "管理员",
			Email:      "<EMAIL>",
			Phone:      "15888888888",
			Sex:        "1",
			Avatar:     "",
			Password:   "",
			Status:     "0",
			DelFlag:    "0",
			LoginIP:    "127.0.0.1",
			LoginDate:  time.Now(),
			CreateBy:   "admin",
			CreateTime: time.Now(),
			UpdateBy:   "admin",
			UpdateTime: time.Now(),
			Remark:     "管理员",
		}
	} else if userID == 2 {
		user = &model.TestUser{
			ID:         2,
			UserName:   "test",
			NickName:   "测试用户",
			Email:      "<EMAIL>",
			Phone:      "15666666666",
			Sex:        "0",
			Avatar:     "",
			Password:   "",
			Status:     "0",
			DelFlag:    "0",
			LoginIP:    "127.0.0.1",
			LoginDate:  time.Now(),
			CreateBy:   "admin",
			CreateTime: time.Now(),
			UpdateBy:   "admin",
			UpdateTime: time.Now(),
			Remark:     "测试用户",
		}
	}

	// 检查用户是否存在
	if user == nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  fmt.Sprintf("用户ID(%d)不存在", userID),
		})
		return
	}

	// 返回结果
	c.Success(ctx, user)
}

// Save 新增用户
// @Summary 新增用户
// @Description 新增用户
// @Tags 测试工具
// @Accept json
// @Produce json
// @Param user body model.TestUser true "用户信息"
// @Success 200 {object} controller.Response "成功"
// @Router /test/user/save [post]
func (c *TestController) Save(ctx *gin.Context) {
	// 绑定请求参数
	var user model.TestUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.Error(ctx, err)
		return
	}

	// 模拟保存用户
	user.ID = 3 // 模拟生成ID
	user.CreateTime = time.Now()
	user.UpdateTime = time.Now()

	// 返回结果
	c.Success(ctx, user)
}

// Update 修改用户
// @Summary 修改用户
// @Description 修改用户
// @Tags 测试工具
// @Accept json
// @Produce json
// @Param user body model.TestUser true "用户信息"
// @Success 200 {object} controller.Response "成功"
// @Router /test/user/update [put]
func (c *TestController) Update(ctx *gin.Context) {
	// 绑定请求参数
	var user model.TestUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.Error(ctx, err)
		return
	}

	// 检查用户ID是否有效
	if user.ID <= 0 {
		c.Error(ctx, fmt.Errorf("用户ID无效"))
		return
	}

	// 模拟更新用户
	user.UpdateTime = time.Now()

	// 返回结果
	c.Success(ctx, user)
}

// Delete 删除用户
// @Summary 删除用户
// @Description 删除用户
// @Tags 测试工具
// @Accept json
// @Produce json
// @Param userId path int true "用户ID"
// @Success 200 {object} controller.Response "成功"
// @Router /test/user/{userId} [delete]
func (c *TestController) Delete(ctx *gin.Context) {
	// 获取用户ID
	userIDStr := ctx.Param("userId")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		c.Error(ctx, fmt.Errorf("用户ID无效"))
		return
	}

	// 模拟删除用户
	if userID != 1 && userID != 2 {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  fmt.Sprintf("用户ID(%d)不存在", userID),
		})
		return
	}

	// 返回结果
	c.Success(ctx, nil)
}
