package service

import "backend/internal/model"

// SysPermissionService 权限服务接口
type SysPermissionService interface {
	// GetRolePermission 获取角色数据权限
	GetRolePermission(user *model.SysUser) []string

	// GetMenuPermission 获取菜单数据权限
	GetMenuPermission(user *model.SysUser) []string

	// HasPermission 判断是否有某个权限
	HasPermission(permissions []string, permission string) bool

	// HasRole 判断是否有某个角色
	HasRole(role string, roles []string) bool
}
