package server

import (
	"backend/internal/utils"
)

// Cpu CPU相关信息
type Cpu struct {
	// 核心数
	CpuNum int `json:"cpuNum"`

	// CPU总的使用率
	Total float64 `json:"total"`

	// CPU系统使用率
	Sys float64 `json:"sys"`

	// CPU用户使用率
	Used float64 `json:"used"`

	// CPU当前等待率
	Wait float64 `json:"wait"`

	// CPU当前空闲率
	Free float64 `json:"free"`
}

// GetTotal 获取CPU总的使用率
func (c *Cpu) GetTotal() float64 {
	return utils.Arith.Round(utils.Arith.Mul(c.Total, 100), 2)
}

// GetSys 获取CPU系统使用率
func (c *Cpu) GetSys() float64 {
	if c.Total == 0 {
		return 0
	}
	return utils.Arith.Round(utils.Arith.Mul(utils.Arith.Div(c.Sys, c.Total, 4), 100), 2)
}

// GetUsed 获取CPU用户使用率
func (c *Cpu) GetUsed() float64 {
	if c.Total == 0 {
		return 0
	}
	return utils.Arith.Round(utils.Arith.Mul(utils.Arith.Div(c.Used, c.Total, 4), 100), 2)
}

// GetWait 获取CPU当前等待率
func (c *Cpu) GetWait() float64 {
	if c.Total == 0 {
		return 0
	}
	return utils.Arith.Round(utils.Arith.Mul(utils.Arith.Div(c.Wait, c.Total, 4), 100), 2)
}

// GetFree 获取CPU当前空闲率
func (c *Cpu) GetFree() float64 {
	if c.Total == 0 {
		return 0
	}
	return utils.Arith.Round(utils.Arith.Mul(utils.Arith.Div(c.Free, c.Total, 4), 100), 2)
}
