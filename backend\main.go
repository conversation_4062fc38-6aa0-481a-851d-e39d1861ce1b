package main

import (
	"backend/internal/api/router"
	"backend/internal/model"
	"backend/internal/repository"
	"fmt"
	"log"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	fmt.Println("开始初始化数据库...")
	// 初始化数据库连接
	db, err := initDB()
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	fmt.Println("数据库初始化成功，设置路由...")
	// 设置数据库连接到路由
	router.SetDB(db)

	// 创建gin实例
	r := gin.Default()

	// 初始化路由
	router.InitRouter(r)

	fmt.Println("启动服务在端口8080...")
	// 启动服务
	err = r.Run(":8080")
	if err != nil {
		log.Fatalf("服务启动失败: %v", err)
	}
}

// initDB 初始化数据库连接
func initDB() (*gorm.DB, error) {
	fmt.Println("配置数据库日志...")
	// 配置GORM日志
	newLogger := logger.New(
		log.New(log.Writer(), "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold: time.Second,
			LogLevel:      logger.Info,
			Colorful:      true,
		},
	)

	fmt.Println("尝试连接SQL Server数据库...")
	// 连接SQL Server数据库
	// 使用Windows身份验证模式
	dsn := "sqlserver://localhost:1433?database=wosm&encrypt=disable&TrustServerCertificate=true&integrated security=true"
	fmt.Printf("使用连接字符串: %s\n", dsn)

	db, err := gorm.Open(sqlserver.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		fmt.Printf("Windows身份验证连接错误，尝试使用SQL身份验证: %v\n", err)

		// 尝试使用SQL身份验证
		dsn = "sqlserver://sa:F@2233@localhost:1433?database=wosm&encrypt=disable&TrustServerCertificate=true"
		fmt.Printf("使用SQL身份验证连接字符串: %s\n", dsn)

		db, err = gorm.Open(sqlserver.Open(dsn), &gorm.Config{
			Logger: newLogger,
		})
		if err != nil {
			fmt.Printf("SQL身份验证连接错误: %v\n", err)
			return nil, fmt.Errorf("数据库连接失败: %w", err)
		}
	}

	fmt.Println("获取底层SQL DB连接...")
	// 获取底层SQL DB连接
	sqlDB, err := db.DB()
	if err != nil {
		fmt.Printf("获取底层DB错误: %v\n", err)
		return nil, fmt.Errorf("获取底层DB失败: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	fmt.Println("测试数据库连接...")
	// 测试数据库连接
	if err := sqlDB.Ping(); err != nil {
		fmt.Printf("数据库Ping错误: %v\n", err)
		return nil, fmt.Errorf("数据库Ping失败: %w", err)
	}

	fmt.Println("数据库连接成功")

	// 检查数据库表是否存在
	var tableCount int64
	result := db.Raw("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_CATALOG = 'wosm' AND TABLE_NAME = 'sys_user'").Scan(&tableCount)
	if result.Error != nil {
		fmt.Printf("检查表存在错误: %v\n", result.Error)
		return nil, fmt.Errorf("检查表存在失败: %w", result.Error)
	}

	if tableCount > 0 {
		fmt.Println("数据库表已存在，跳过自动迁移")
	} else {
		fmt.Println("开始自动迁移数据库表...")
		// 自动迁移数据库表
		err = autoMigrate(db)
		if err != nil {
			fmt.Printf("自动迁移错误: %v\n", err)
			return nil, fmt.Errorf("自动迁移失败: %w", err)
		}
		fmt.Println("自动迁移数据库表完成")
	}

	// 初始化系统数据
	fmt.Println("开始初始化系统数据...")
	if err := repository.InitSystemData(db); err != nil {
		fmt.Printf("初始化系统数据错误: %v\n", err)
		// 这里不返回错误，让程序继续运行
	}

	return db, nil
}

// autoMigrate 自动迁移数据库表
func autoMigrate(db *gorm.DB) error {
	// 迁移系统表
	return db.AutoMigrate(
		&model.SysUser{},
		&model.SysRole{},
		&model.SysMenu{},
		&model.SysDept{},
		&model.SysPost{},
		&model.SysUserRole{},
		&model.SysRoleMenu{},
		&model.SysRoleDept{},
		&model.SysUserPost{},
		&model.SysConfig{}, // 添加配置表迁移
		&model.SysJob{},    // 添加定时任务表迁移
		&model.SysJobLog{}, // 添加定时任务日志表迁移
	)
}
