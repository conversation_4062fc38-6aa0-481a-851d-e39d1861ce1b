package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysUserPostRepository 用户岗位关联Repository接口
type SysUserPostRepository interface {
	Repository
	// DeleteUserPostByUserId 通过用户ID删除用户和岗位关联
	DeleteUserPostByUserId(userId int64) error
	// DeleteUserPostByPostId 通过岗位ID删除用户和岗位关联
	DeleteUserPostByPostId(postId int64) error
	// CountUserPostByPostId 通过岗位ID查询对应的用户数量
	CountUserPostByPostId(postId int64) (int64, error)
	// DeleteUserPost 删除用户和岗位关联信息
	DeleteUserPost(userPost *model.SysUserPost) error
	// InsertUserPost 批量新增用户岗位信息
	InsertUserPost(userId int64, postIds []int64) error
}

// SysUserPostRepositoryImpl 用户岗位关联Repository实现
type SysUserPostRepositoryImpl struct {
	*BaseRepository
}

// NewSysUserPostRepository 创建用户岗位关联Repository
func NewSysUserPostRepository(db *gorm.DB) SysUserPostRepository {
	return &SysUserPostRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// DeleteUserPostByUserId 通过用户ID删除用户和岗位关联
func (r *SysUserPostRepositoryImpl) DeleteUserPostByUserId(userId int64) error {
	return r.DB.Where("user_id = ?", userId).Delete(&model.SysUserPost{}).Error
}

// DeleteUserPostByPostId 通过岗位ID删除用户和岗位关联
func (r *SysUserPostRepositoryImpl) DeleteUserPostByPostId(postId int64) error {
	return r.DB.Where("post_id = ?", postId).Delete(&model.SysUserPost{}).Error
}

// CountUserPostByPostId 通过岗位ID查询对应的用户数量
func (r *SysUserPostRepositoryImpl) CountUserPostByPostId(postId int64) (int64, error) {
	var count int64
	err := r.DB.Model(&model.SysUserPost{}).Where("post_id = ?", postId).Count(&count).Error
	return count, err
}

// DeleteUserPost 删除用户和岗位关联信息
func (r *SysUserPostRepositoryImpl) DeleteUserPost(userPost *model.SysUserPost) error {
	return r.DB.Delete(userPost).Error
}

// InsertUserPost 批量新增用户岗位信息
func (r *SysUserPostRepositoryImpl) InsertUserPost(userId int64, postIds []int64) error {
	var userPosts []model.SysUserPost
	for _, postId := range postIds {
		userPosts = append(userPosts, model.SysUserPost{
			UserID: userId,
			PostID: postId,
		})
	}
	return r.DB.Create(&userPosts).Error
}
