# 服务配置
server:
  # 服务端口
  port: 8080
  # 服务名称
  name: RuoYi-Go
  # 版本
  version: 1.0.0
  # 运行模式 debug/release
  mode: debug
  # 接口前缀
  context-path: /
  
# 日志配置
log:
  # 日志级别 debug/info/warn/error
  level: info
  # 日志路径
  path: ./logs
  # 是否输出到控制台
  console: true
  # 日志文件最大大小（MB）
  max-size: 100
  # 日志最大保留天数
  max-age: 30
  # 日志最大保留个数
  max-backups: 10
  # 是否启用JSON格式
  json-format: false
  # 是否压缩
  compress: false

# 数据库配置
database:
  # 数据库类型 mysql/postgres/sqlserver
  type: sqlserver
  # 主机
  host: 127.0.0.1
  # 端口
  port: 1433
  # 用户名
  username: sa
  # 密码
  password: yourpassword
  # 数据库名
  database: ruoyi
  # 额外参数
  params: "encrypt=disable"
  # 最大空闲连接数
  max-idle-conns: 10
  # 最大打开连接数
  max-open-conns: 100
  # 连接最大存活时间（分钟）
  conn-max-lifetime: 60
  # 打印SQL
  log-mode: true
  # 慢查询阈值（毫秒）
  slow-threshold: 200

# Redis配置
redis:
  # 主机
  host: 127.0.0.1
  # 端口
  port: 6379
  # 密码
  password: 
  # 数据库
  database: 0
  # 连接池大小
  pool-size: 100
  # 最小空闲连接数
  min-idle-conns: 10
  # 空闲超时时间（分钟）
  idle-timeout: 10
  # 连接超时时间（秒）
  dial-timeout: 5
  # 读取超时时间（秒）
  read-timeout: 3
  # 写入超时时间（秒）
  write-timeout: 3

# JWT配置
jwt:
  # 密钥
  secret: ruoyi-go-secret-key
  # 过期时间（分钟）
  expire-time: 120
  # 缓冲时间（分钟）
  buffer-time: 30
  # 签发者
  issuer: ruoyi-go

# 上传配置
upload:
  # 上传路径
  path: ./uploads
  # 资源访问路径
  resource-prefix: /profile
  # 允许的文件类型
  allowed-ext: .jpg,.jpeg,.png,.gif,.doc,.docx,.pdf,.xls,.xlsx,.ppt,.pptx,.txt
  # 最大文件大小（MB）
  max-size: 50

# 验证码配置
captcha:
  # 验证码类型 math/char/chinese
  type: math
  # 过期时间（分钟）
  expire-time: 2
  # 验证码长度
  length: 4
  # 验证码宽度
  width: 160
  # 验证码高度
  height: 60
  # 是否开启验证码
  enabled: true

# 用户配置
user:
  # 密码最大错误次数
  password-max-retry-count: 5
  # 密码锁定时间（分钟）
  password-lock-time: 10
  # 初始密码
  default-password: 123456
  # 密码长度限制
  password-min-length: 5
  password-max-length: 20
  # 是否允许用户注册
  allow-register: false

# 安全配置
security:
  # 是否开启XSS防护
  xss-enabled: true
  # XSS排除路径
  xss-excludes: /system/notice
  # 是否开启CSRF防护
  csrf-enabled: false
  # 允许的域名
  allowed-origins: "*"
  # 允许的方法
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  # 允许的头部
  allowed-headers: Content-Type,Authorization
  # 允许的IP
  allowed-ips: 

# 定时任务配置
job:
  # 是否开启定时任务
  enabled: true
  # 是否开启并发执行
  concurrent: true
  # 最大历史记录数
  max-history: 100
  # 白名单
  whitelist: com.ruoyi

# 生成配置
gen:
  # 作者
  author: ruoyi
  # 默认生成包路径
  package-name: com.ruoyi.system
  # 自动去除表前缀
  auto-remove-pre: true
  # 表前缀
  table-prefix: sys_ 