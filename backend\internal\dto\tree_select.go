package dto

import "backend/internal/model"

// TreeSelect 树结构实体类
type TreeSelect struct {
	// 节点ID
	ID int64 `json:"id"`

	// 节点名称
	Label string `json:"label"`

	// 节点禁用
	Disabled bool `json:"disabled,omitempty"`

	// 子节点
	Children []*TreeSelect `json:"children,omitempty"`
}

// NewDeptTreeSelect 根据部门创建树选择
func NewDeptTreeSelect(dept *model.SysDept) *TreeSelect {
	ts := &TreeSelect{
		ID:       dept.DeptID,
		Label:    dept.DeptName,
		Disabled: dept.Status == "1", // 1表示停用
	}

	if dept.Children != nil {
		ts.Children = make([]*TreeSelect, 0, len(dept.Children))
		for _, child := range dept.Children {
			ts.Children = append(ts.Children, NewDeptTreeSelect(child))
		}
	}

	return ts
}

// NewMenuTreeSelect 根据菜单创建树选择
func NewMenuTreeSelect(menu *model.SysMenu) *TreeSelect {
	ts := &TreeSelect{
		ID:    menu.MenuID,
		Label: menu.MenuName,
	}

	if menu.Children != nil {
		ts.Children = make([]*TreeSelect, 0, len(menu.Children))
		for _, child := range menu.Children {
			ts.Children = append(ts.Children, NewMenuTreeSelect(child))
		}
	}

	return ts
}
