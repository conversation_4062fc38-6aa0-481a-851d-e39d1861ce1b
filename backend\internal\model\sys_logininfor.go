package model

import (
	"time"
)

// SysLogininfor 系统访问记录表
type SysLogininfor struct {
	BaseModel
	// ID
	InfoId int64 `json:"infoId" gorm:"column:info_id;primary_key;auto_increment;comment:ID"`
	// 用户账号
	UserName string `json:"userName" gorm:"column:user_name;comment:用户账号"`
	// 登录状态 0成功 1失败
	Status string `json:"status" gorm:"column:status;comment:登录状态 0成功 1失败"`
	// 登录IP地址
	Ipaddr string `json:"ipaddr" gorm:"column:ipaddr;comment:登录IP地址"`
	// 登录地点
	LoginLocation string `json:"loginLocation" gorm:"column:login_location;comment:登录地点"`
	// 浏览器类型
	Browser string `json:"browser" gorm:"column:browser;comment:浏览器类型"`
	// 操作系统
	OS string `json:"os" gorm:"column:os;comment:操作系统"`
	// 提示消息
	Msg string `json:"msg" gorm:"column:msg;comment:提示消息"`
	// 访问时间
	LoginTime *time.Time `json:"loginTime" gorm:"column:login_time;comment:访问时间"`
}

// TableName 设置表名
func (SysLogininfor) TableName() string {
	return "sys_logininfor"
}
