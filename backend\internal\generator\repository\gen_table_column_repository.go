package repository

import (
	"backend/internal/model"
	"strings"

	"gorm.io/gorm"
)

// GenTableColumnRepository 代码生成表字段数据访问接口
type GenTableColumnRepository interface {
	// SelectGenTableColumnListByTableId 根据表ID查询表字段列表
	SelectGenTableColumnListByTableId(tableId int64) ([]*model.GenTableColumn, error)

	// InsertGenTableColumn 新增业务字段
	InsertGenTableColumn(genTableColumn *model.GenTableColumn) error

	// UpdateGenTableColumn 修改业务字段
	UpdateGenTableColumn(genTableColumn *model.GenTableColumn) error

	// DeleteGenTableColumnByIds 批量删除业务字段
	DeleteGenTableColumnByIds(ids []int64) error

	// SelectDbTableColumnsByName 根据表名查询列信息
	SelectDbTableColumnsByName(tableName string) ([]*model.GenTableColumn, error)
}

// genTableColumnRepository 代码生成表字段数据访问实现
type genTableColumnRepository struct {
	db *gorm.DB
}

// NewGenTableColumnRepository 创建代码生成表字段数据访问实现
func NewGenTableColumnRepository(db *gorm.DB) GenTableColumnRepository {
	return &genTableColumnRepository{
		db: db,
	}
}

// SelectGenTableColumnListByTableId 根据表ID查询表字段列表
func (r *genTableColumnRepository) SelectGenTableColumnListByTableId(tableId int64) ([]*model.GenTableColumn, error) {
	var columns []*model.GenTableColumn
	err := r.db.Where("table_id = ?", tableId).Order("sort").Find(&columns).Error
	return columns, err
}

// InsertGenTableColumn 新增业务字段
func (r *genTableColumnRepository) InsertGenTableColumn(genTableColumn *model.GenTableColumn) error {
	return r.db.Create(genTableColumn).Error
}

// UpdateGenTableColumn 修改业务字段
func (r *genTableColumnRepository) UpdateGenTableColumn(genTableColumn *model.GenTableColumn) error {
	return r.db.Save(genTableColumn).Error
}

// DeleteGenTableColumnByIds 批量删除业务字段
func (r *genTableColumnRepository) DeleteGenTableColumnByIds(ids []int64) error {
	return r.db.Where("column_id in ?", ids).Delete(&model.GenTableColumn{}).Error
}

// SelectDbTableColumnsByName 根据表名查询列信息
func (r *genTableColumnRepository) SelectDbTableColumnsByName(tableName string) ([]*model.GenTableColumn, error) {
	// 这里需要根据不同数据库类型实现不同的查询逻辑
	// 以SQL Server为例
	var columns []*model.GenTableColumn

	// 构建查询SQL
	query := `SELECT
		c.name AS column_name,
		t.name AS column_type,
		ISNULL(ep.[value], '') AS column_comment,
		CASE WHEN pk.column_id IS NOT NULL THEN '1' ELSE '0' END AS is_pk,
		CASE WHEN c.is_identity = 1 THEN '1' ELSE '0' END AS is_increment,
		CASE WHEN c.is_nullable = 1 THEN '0' ELSE '1' END AS is_required,
		CAST(c.column_id AS INT) AS sort
	FROM
		sys.columns c
	INNER JOIN sys.types t ON c.user_type_id = t.user_type_id
	INNER JOIN sys.objects obj ON obj.object_id = c.object_id
	LEFT JOIN sys.extended_properties ep ON ep.major_id = c.object_id AND ep.minor_id = c.column_id AND ep.class = 1
	LEFT JOIN (
		SELECT
			ic.column_id,
			ic.object_id
		FROM
			sys.index_columns ic
		INNER JOIN sys.indexes i ON ic.object_id = i.object_id AND ic.index_id = i.index_id
		WHERE
			i.is_primary_key = 1
	) pk ON pk.object_id = c.object_id AND pk.column_id = c.column_id
	WHERE
		obj.name = ?
	ORDER BY
		c.column_id`

	// 执行查询
	rows, err := r.db.Raw(query, tableName).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 处理结果
	for rows.Next() {
		var columnName, columnType, columnComment, isPk, isIncrement, isRequired string
		var sort int
		if err := rows.Scan(&columnName, &columnType, &columnComment, &isPk, &isIncrement, &isRequired, &sort); err != nil {
			return nil, err
		}

		// 处理字段类型
		javaType := getJavaType(columnType)
		javaField := formatJavaField(columnName)

		column := &model.GenTableColumn{
			ColumnName:    columnName,
			ColumnType:    columnType,
			ColumnComment: columnComment,
			IsPk:          isPk,
			IsIncrement:   isIncrement,
			IsRequired:    isRequired,
			JavaType:      javaType,
			JavaField:     javaField,
			IsInsert:      "1",
			IsEdit:        "1",
			IsList:        "1",
			IsQuery:       "1",
			QueryType:     "EQ",
			HtmlType:      getHtmlType(columnType),
			Sort:          sort,
		}

		columns = append(columns, column)
	}

	return columns, nil
}

// getJavaType 获取Java类型
func getJavaType(columnType string) string {
	// SQL Server类型映射到Java类型
	columnType = strings.ToLower(columnType)

	switch {
	case strings.Contains(columnType, "char") || strings.Contains(columnType, "text"):
		return "String"
	case strings.Contains(columnType, "int") || strings.Contains(columnType, "smallint"):
		return "Integer"
	case strings.Contains(columnType, "bigint"):
		return "Long"
	case strings.Contains(columnType, "float") || strings.Contains(columnType, "real"):
		return "Float"
	case strings.Contains(columnType, "double") || strings.Contains(columnType, "money") || strings.Contains(columnType, "decimal"):
		return "Double"
	case strings.Contains(columnType, "date") || strings.Contains(columnType, "time"):
		return "Date"
	case strings.Contains(columnType, "bit") || strings.Contains(columnType, "boolean"):
		return "Boolean"
	default:
		return "String"
	}
}

// getHtmlType 获取HTML类型
func getHtmlType(columnType string) string {
	// 根据列类型获取HTML类型
	columnType = strings.ToLower(columnType)

	switch {
	case strings.Contains(columnType, "date") || strings.Contains(columnType, "time"):
		return "datetime"
	case strings.Contains(columnType, "text"):
		return "textarea"
	case strings.Contains(columnType, "bit") || strings.Contains(columnType, "boolean"):
		return "radio"
	default:
		return "input"
	}
}

// formatJavaField 格式化Java字段
func formatJavaField(columnName string) string {
	// 将下划线命名转换为驼峰命名
	parts := strings.Split(columnName, "_")
	result := strings.ToLower(parts[0])

	for i := 1; i < len(parts); i++ {
		if parts[i] != "" {
			result += strings.ToUpper(parts[i][:1]) + strings.ToLower(parts[i][1:])
		}
	}

	return result
}
