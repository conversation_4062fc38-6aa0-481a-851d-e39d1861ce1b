package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysConfigRepository 系统配置仓库接口
type SysConfigRepository interface {
	// SelectConfigById 查询参数配置信息
	SelectConfigById(configId int64) (*model.SysConfig, error)

	// SelectConfigByKey 根据键名查询参数配置信息
	SelectConfigByKey(configKey string) (*model.SysConfig, error)

	// SelectConfigList 查询参数配置列表
	SelectConfigList(config *model.SysConfig) ([]*model.SysConfig, error)

	// InsertConfig 新增参数配置
	InsertConfig(config *model.SysConfig) (int64, error)

	// UpdateConfig 修改参数配置
	UpdateConfig(config *model.SysConfig) (int64, error)

	// DeleteConfigByIds 批量删除参数信息
	DeleteConfigByIds(configIds []int64) error

	// CheckConfigKeyUnique 校验参数键名是否唯一
	CheckConfigKeyUnique(configKey string, configId int64) (*model.SysConfig, error)
}

// SysConfigRepositoryImpl 参数配置Repository实现
type SysConfigRepositoryImpl struct {
	*BaseRepository
}

// NewSysConfigRepository 创建参数配置Repository
func NewSysConfigRepository(db *gorm.DB) SysConfigRepository {
	return &SysConfigRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// SelectConfigById 根据参数ID查询参数
func (r *SysConfigRepositoryImpl) SelectConfigById(configId int64) (*model.SysConfig, error) {
	var config model.SysConfig
	err := r.DB.Where("config_id = ?", configId).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// SelectConfigByKey 根据参数键名查询参数
func (r *SysConfigRepositoryImpl) SelectConfigByKey(configKey string) (*model.SysConfig, error) {
	var config model.SysConfig
	err := r.DB.Where("config_key = ?", configKey).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// SelectConfigList 查询参数配置列表
func (r *SysConfigRepositoryImpl) SelectConfigList(config *model.SysConfig) ([]*model.SysConfig, error) {
	var configs []*model.SysConfig
	db := r.DB.Model(&model.SysConfig{})

	if config.ConfigName != "" {
		db = db.Where("config_name LIKE ?", "%"+config.ConfigName+"%")
	}
	if config.ConfigKey != "" {
		db = db.Where("config_key LIKE ?", "%"+config.ConfigKey+"%")
	}
	if config.ConfigType != "" {
		db = db.Where("config_type = ?", config.ConfigType)
	}

	err := db.Find(&configs).Error
	return configs, err
}

// InsertConfig 新增参数配置
func (r *SysConfigRepositoryImpl) InsertConfig(config *model.SysConfig) (int64, error) {
	err := r.DB.Create(config).Error
	if err != nil {
		return 0, err
	}
	return config.ConfigID, nil
}

// UpdateConfig 修改参数配置
func (r *SysConfigRepositoryImpl) UpdateConfig(config *model.SysConfig) (int64, error) {
	err := r.DB.Updates(config).Error
	if err != nil {
		return 0, err
	}
	return int64(r.DB.RowsAffected), nil
}

// DeleteConfigByIds 批量删除参数信息
func (r *SysConfigRepositoryImpl) DeleteConfigByIds(configIds []int64) error {
	return r.DB.Delete(&model.SysConfig{}, "config_id IN ?", configIds).Error
}

// CheckConfigKeyUnique 校验参数键名是否唯一
func (r *SysConfigRepositoryImpl) CheckConfigKeyUnique(configKey string, configId int64) (*model.SysConfig, error) {
	var config model.SysConfig
	err := r.DB.Where("config_key = ?", configKey).First(&config).Error
	if err != nil {
		return nil, err
	}
	if config.ConfigID != configId {
		return &config, nil
	}
	return nil, nil
}
