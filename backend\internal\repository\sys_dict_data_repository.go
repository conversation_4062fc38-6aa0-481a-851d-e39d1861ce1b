package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysDictDataRepository 字典数据仓库接口
type SysDictDataRepository interface {
	// SelectDictDataList 查询字典数据列表
	SelectDictDataList(dictData *model.SysDictData) ([]*model.SysDictData, error)

	// SelectDictDataById 根据字典数据ID查询信息
	SelectDictDataById(dictCode int64) (*model.SysDictData, error)

	// SelectDictDataByType 根据字典类型查询字典数据
	SelectDictDataByType(dictType string) ([]*model.SysDictData, error)

	// SelectDictLabel 根据字典类型和字典值获取字典标签
	SelectDictLabel(dictType, dictValue string) (string, error)

	// DeleteDictDataById 删除字典数据
	DeleteDictDataById(dictCode int64) error

	// DeleteDictDataByIds 批量删除字典数据
	DeleteDictDataByIds(dictCodes []int64) error

	// InsertDictData 新增字典数据
	InsertDictData(dictData *model.SysDictData) (int64, error)

	// UpdateDictData 修改字典数据
	UpdateDictData(dictData *model.SysDictData) (int64, error)

	// CountDictDataByType 根据字典类型统计字典数据数量
	CountDictDataByType(dictType string) (int64, error)
}

// SysDictDataRepositoryImpl 字典数据仓库实现
type SysDictDataRepositoryImpl struct {
	*BaseRepository
}

// NewSysDictDataRepository 创建字典数据仓库
func NewSysDictDataRepository(db *gorm.DB) SysDictDataRepository {
	return &SysDictDataRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// SelectDictDataList 查询字典数据列表
func (r *SysDictDataRepositoryImpl) SelectDictDataList(dictData *model.SysDictData) ([]*model.SysDictData, error) {
	var list []*model.SysDictData
	db := r.DB.Model(&model.SysDictData{})

	if dictData.DictType != "" {
		db = db.Where("dict_type = ?", dictData.DictType)
	}
	if dictData.DictLabel != "" {
		db = db.Where("dict_label LIKE ?", "%"+dictData.DictLabel+"%")
	}
	if dictData.Status != "" {
		db = db.Where("status = ?", dictData.Status)
	}

	err := db.Order("dict_sort").Find(&list).Error
	return list, err
}

// SelectDictDataById 根据字典数据ID查询信息
func (r *SysDictDataRepositoryImpl) SelectDictDataById(dictCode int64) (*model.SysDictData, error) {
	var dictData model.SysDictData
	err := r.DB.Where("dict_code = ?", dictCode).First(&dictData).Error
	if err != nil {
		return nil, err
	}
	return &dictData, nil
}

// SelectDictDataByType 根据字典类型查询字典数据
func (r *SysDictDataRepositoryImpl) SelectDictDataByType(dictType string) ([]*model.SysDictData, error) {
	var list []*model.SysDictData
	err := r.DB.Where("dict_type = ? AND status = '0'", dictType).Order("dict_sort").Find(&list).Error
	return list, err
}

// SelectDictLabel 根据字典类型和字典值获取字典标签
func (r *SysDictDataRepositoryImpl) SelectDictLabel(dictType, dictValue string) (string, error) {
	var dictData model.SysDictData
	err := r.DB.Where("dict_type = ? AND dict_value = ?", dictType, dictValue).First(&dictData).Error
	if err != nil {
		return "", err
	}
	return dictData.DictLabel, nil
}

// DeleteDictDataById 删除字典数据
func (r *SysDictDataRepositoryImpl) DeleteDictDataById(dictCode int64) error {
	return r.DB.Delete(&model.SysDictData{}, dictCode).Error
}

// DeleteDictDataByIds 批量删除字典数据
func (r *SysDictDataRepositoryImpl) DeleteDictDataByIds(dictCodes []int64) error {
	return r.DB.Delete(&model.SysDictData{}, "dict_code IN ?", dictCodes).Error
}

// InsertDictData 新增字典数据
func (r *SysDictDataRepositoryImpl) InsertDictData(dictData *model.SysDictData) (int64, error) {
	err := r.DB.Create(dictData).Error
	if err != nil {
		return 0, err
	}
	return dictData.DictCode, nil
}

// UpdateDictData 修改字典数据
func (r *SysDictDataRepositoryImpl) UpdateDictData(dictData *model.SysDictData) (int64, error) {
	err := r.DB.Updates(dictData).Error
	if err != nil {
		return 0, err
	}
	return int64(r.DB.RowsAffected), nil
}

// CountDictDataByType 根据字典类型统计字典数据数量
func (r *SysDictDataRepositoryImpl) CountDictDataByType(dictType string) (int64, error) {
	var count int64
	err := r.DB.Model(&model.SysDictData{}).Where("dict_type = ?", dictType).Count(&count).Error
	return count, err
}
