package impl

import (
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
)

// SysNoticeServiceImpl 通知公告服务实现
type SysNoticeServiceImpl struct {
	noticeRepo repository.SysNoticeRepository
}

// NewSysNoticeService 创建通知公告服务
func NewSysNoticeService(noticeRepo repository.SysNoticeRepository) service.SysNoticeService {
	return &SysNoticeServiceImpl{
		noticeRepo: noticeRepo,
	}
}

// SelectNoticeById 查询公告信息
func (s *SysNoticeServiceImpl) SelectNoticeById(noticeId int64) (*model.SysNotice, error) {
	return s.noticeRepo.SelectNoticeById(noticeId)
}

// SelectNoticeList 查询公告列表
func (s *SysNoticeServiceImpl) SelectNoticeList(notice *model.SysNotice) ([]*model.SysNotice, error) {
	return s.noticeRepo.SelectNoticeList(notice)
}

// InsertNotice 新增公告
func (s *SysNoticeServiceImpl) InsertNotice(notice *model.SysNotice) (int64, error) {
	return s.noticeRepo.InsertNotice(notice)
}

// UpdateNotice 修改公告
func (s *SysNoticeServiceImpl) UpdateNotice(notice *model.SysNotice) (int64, error) {
	return s.noticeRepo.UpdateNotice(notice)
}

// DeleteNoticeById 删除公告信息
func (s *SysNoticeServiceImpl) DeleteNoticeById(noticeId int64) error {
	return s.noticeRepo.DeleteNoticeById(noticeId)
}

// DeleteNoticeByIds 批量删除公告信息
func (s *SysNoticeServiceImpl) DeleteNoticeByIds(noticeIds []int64) error {
	return s.noticeRepo.DeleteNoticeByIds(noticeIds)
}
