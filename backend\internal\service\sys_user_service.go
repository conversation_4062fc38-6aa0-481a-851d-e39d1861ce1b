package service

import (
	"backend/internal/model"
)

// SysUserService 用户业务层接口
type SysUserService interface {
	// SelectUserList 根据条件分页查询用户列表
	SelectUserList(user *model.SysUser) ([]*model.SysUser, error)

	// SelectAllocatedList 根据条件分页查询已分配用户角色列表
	SelectAllocatedList(user *model.SysUser) ([]*model.SysUser, error)

	// SelectUnallocatedList 根据条件分页查询未分配用户角色列表
	SelectUnallocatedList(user *model.SysUser) ([]*model.SysUser, error)

	// SelectUserByUserName 通过用户名查询用户
	SelectUserByUserName(userName string) (*model.SysUser, error)

	// SelectUserById 通过用户ID查询用户
	SelectUserById(userId int64) (*model.SysUser, error)

	// SelectUserRoleGroup 根据用户ID查询用户所属角色组
	SelectUserRoleGroup(userName string) (string, error)

	// SelectUserPostGroup 根据用户ID查询用户所属岗位组
	SelectUserPostGroup(userName string) (string, error)

	// CheckUserNameUnique 校验用户名称是否唯一
	CheckUserNameUnique(user *model.SysUser) bool

	// CheckPhoneUnique 校验手机号码是否唯一
	CheckPhoneUnique(user *model.SysUser) bool

	// CheckEmailUnique 校验email是否唯一
	CheckEmailUnique(user *model.SysUser) bool

	// CheckUserAllowed 校验用户是否允许操作
	CheckUserAllowed(user *model.SysUser) error

	// CheckUserDataScope 校验用户是否有数据权限
	CheckUserDataScope(userId int64) error

	// InsertUser 新增用户信息
	InsertUser(user *model.SysUser) (int64, error)

	// RegisterUser 注册用户信息
	RegisterUser(user *model.SysUser) bool

	// UpdateUser 修改用户信息
	UpdateUser(user *model.SysUser) int

	// InsertUserAuth 用户授权角色
	InsertUserAuth(userId int64, roleIds []int64) error

	// UpdateUserStatus 修改用户状态
	UpdateUserStatus(user *model.SysUser) int

	// UpdateUserProfile 修改用户基本信息
	UpdateUserProfile(user *model.SysUser) int

	// UpdateUserAvatar 修改用户头像
	UpdateUserAvatar(userId int64, avatar string) bool

	// ResetPwd 重置用户密码
	ResetPwd(user *model.SysUser) int

	// ResetUserPwd 重置用户密码
	ResetUserPwd(userId int64, password string) int

	// DeleteUserById 通过用户ID删除用户
	DeleteUserById(userId int64) int

	// DeleteUserByIds 批量删除用户信息
	DeleteUserByIds(userIds []int64) int

	// ImportUser 导入用户数据
	ImportUser(userList []*model.SysUser, isUpdateSupport bool, operName string) string
}
