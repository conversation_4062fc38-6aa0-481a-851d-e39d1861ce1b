package model

// SysDictData 字典数据表 sys_dict_data
type SysDictData struct {
	BaseModel
	// 字典编码
	DictCode int64 `json:"dictCode" gorm:"column:dict_code;primary_key;auto_increment;comment:字典编码"`
	// 字典排序
	DictSort int `json:"dictSort" gorm:"column:dict_sort;default:0;comment:字典排序"`
	// 字典标签
	DictLabel string `json:"dictLabel" gorm:"column:dict_label;comment:字典标签"`
	// 字典键值
	DictValue string `json:"dictValue" gorm:"column:dict_value;comment:字典键值"`
	// 字典类型
	DictType string `json:"dictType" gorm:"column:dict_type;comment:字典类型"`
	// 样式属性（其他样式扩展）
	CssClass string `json:"cssClass" gorm:"column:css_class;comment:样式属性（其他样式扩展）"`
	// 表格回显样式
	ListClass string `json:"listClass" gorm:"column:list_class;comment:表格回显样式"`
	// 是否默认（Y是 N否）
	IsDefault string `json:"isDefault" gorm:"column:is_default;default:N;comment:是否默认（Y是 N否）"`
	// 状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status;default:0;comment:状态（0正常 1停用）"`
}

// TableName 设置表名
func (SysDictData) TableName() string {
	return "sys_dict_data"
}
