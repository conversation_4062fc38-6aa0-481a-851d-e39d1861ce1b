package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysRoleMenuRepository 角色与菜单关联表 Repository接口
type SysRoleMenuRepository interface {
	Repository
	// CheckMenuExistRole 查询菜单使用数量
	CheckMenuExistRole(menuId int64) (int64, error)
	// DeleteRoleMenuByRoleId 通过角色ID删除角色和菜单关联
	DeleteRoleMenuByRoleId(roleId int64) error
	// DeleteRoleMenu 批量删除角色菜单关联信息
	DeleteRoleMenu(roleIds []int64) error
	// BatchRoleMenu 批量新增角色菜单信息
	BatchRoleMenu(roleMenuList []*model.SysRoleMenu) error
}

// SysRoleMenuRepositoryImpl 角色与菜单关联表 Repository实现
type SysRoleMenuRepositoryImpl struct {
	*BaseRepository
}

// NewSysRoleMenuRepository 创建角色与菜单关联表Repository
func NewSysRoleMenuRepository(db *gorm.DB) SysRoleMenuRepository {
	return &SysRoleMenuRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// CheckMenuExistRole 查询菜单使用数量
func (r *SysRoleMenuRepositoryImpl) CheckMenuExistRole(menuId int64) (int64, error) {
	var count int64
	err := r.DB.Model(&model.SysRoleMenu{}).Where("menu_id = ?", menuId).Count(&count).Error
	return count, err
}

// DeleteRoleMenuByRoleId 通过角色ID删除角色和菜单关联
func (r *SysRoleMenuRepositoryImpl) DeleteRoleMenuByRoleId(roleId int64) error {
	return r.DB.Where("role_id = ?", roleId).Delete(&model.SysRoleMenu{}).Error
}

// DeleteRoleMenu 批量删除角色菜单关联信息
func (r *SysRoleMenuRepositoryImpl) DeleteRoleMenu(roleIds []int64) error {
	return r.DB.Where("role_id IN ?", roleIds).Delete(&model.SysRoleMenu{}).Error
}

// BatchRoleMenu 批量新增角色菜单信息
func (r *SysRoleMenuRepositoryImpl) BatchRoleMenu(roleMenuList []*model.SysRoleMenu) error {
	if len(roleMenuList) == 0 {
		return nil
	}
	return r.DB.Create(&roleMenuList).Error
}
