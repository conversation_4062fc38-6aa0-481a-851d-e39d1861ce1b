package model

import "encoding/json"

// SysMenu 菜单权限表 sys_menu
type SysMenu struct {
	BaseModel
	// 菜单ID
	MenuID int64 `json:"menuId" gorm:"primaryKey;column:menu_id"`
	// 菜单名称
	MenuName string `json:"menuName" gorm:"column:menu_name" validate:"required,max=50"`
	// 父菜单名称
	ParentName string `json:"parentName" gorm:"-"`
	// 父菜单ID
	ParentID int64 `json:"parentId" gorm:"column:parent_id"`
	// 显示顺序
	OrderNum int `json:"orderNum" gorm:"column:order_num" validate:"required"`
	// 路由地址
	Path string `json:"path" gorm:"column:path" validate:"max=200"`
	// 组件路径
	Component string `json:"component" gorm:"column:component" validate:"max=255"`
	// 路由参数
	Query string `json:"query" gorm:"column:query"`
	// 路由名称
	RouteName string `json:"routeName" gorm:"column:route_name"`
	// 是否为外链（0是 1否）
	IsFrame string `json:"isFrame" gorm:"column:is_frame"`
	// 是否缓存（0缓存 1不缓存）
	IsCache string `json:"isCache" gorm:"column:is_cache"`
	// 类型（M目录 C菜单 F按钮）
	MenuType string `json:"menuType" gorm:"column:menu_type" validate:"required"`
	// 显示状态（0显示 1隐藏）
	Visible string `json:"visible" gorm:"column:visible"`
	// 菜单状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status"`
	// 权限字符串
	Perms string `json:"perms" gorm:"column:perms" validate:"max=100"`
	// 菜单图标
	Icon string `json:"icon" gorm:"column:icon"`
	// 子菜单
	Children []*SysMenu `json:"children" gorm:"-"`
}

// TableName 设置表名
func (SysMenu) TableName() string {
	return "sys_menu"
}

// MarshalJSON 重写MarshalJSON方法，避免循环引用导致JSON序列化失败
func (m *SysMenu) MarshalJSON() ([]byte, error) {
	type TempSysMenu SysMenu
	return json.Marshal((*TempSysMenu)(m))
}

// ToString 字符串表示
func (m *SysMenu) ToString() string {
	return ToString(m)
}

// MetaVo 前端路由meta属性
type MetaVo struct {
	// 设置该路由在侧边栏和面包屑中展示的名字
	Title string `json:"title"`
	// 设置该路由的图标，对应路径src/assets/icons/svg
	Icon string `json:"icon"`
	// 设置为true，则不会被 <keep-alive>缓存
	NoCache bool `json:"noCache"`
	// 内链地址（http(s)://开头）
	Link string `json:"link,omitempty"`
}

// RouterVo 路由配置信息
type RouterVo struct {
	// 路由名字
	Name string `json:"name"`
	// 路由地址
	Path string `json:"path"`
	// 是否隐藏路由，当设置 true 的时候该路由不会再侧边栏出现
	Hidden bool `json:"hidden"`
	// 重定向地址，当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
	Redirect string `json:"redirect,omitempty"`
	// 组件地址
	Component string `json:"component"`
	// 路由参数：如 {"id": 1, "name": "ry"}
	Query string `json:"query,omitempty"`
	// 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
	AlwaysShow bool `json:"alwaysShow,omitempty"`
	// 其他元素
	Meta MetaVo `json:"meta"`
	// 子路由
	Children []RouterVo `json:"children,omitempty"`
}
