package controller

import (
	"backend/internal/common/response"
	"backend/internal/constants"
	"backend/internal/job/service"
	"backend/internal/model"
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysJobController 定时任务控制器
type SysJobController struct {
	jobService service.SysJobService
}

// NewSysJobController 创建定时任务控制器
func NewSysJobController(jobService service.SysJobService) *SysJobController {
	return &SysJobController{
		jobService: jobService,
	}
}

// List 获取定时任务列表
// @Summary 获取定时任务列表
// @Description 获取定时任务列表
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param jobName query string false "任务名称"
// @Param jobGroup query string false "任务组名"
// @Param status query string false "状态"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job/list [get]
func (c *SysJobController) List(ctx *gin.Context) {
	job := &model.SysJob{
		JobName:  ctx.Query("jobName"),
		JobGroup: ctx.Query("jobGroup"),
		Status:   ctx.Query("status"),
	}

	list, err := c.jobService.SelectJobList(job)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, list)
}

// GetInfo 获取定时任务详细信息
// @Summary 获取定时任务详细信息
// @Description 获取定时任务详细信息
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param jobId path int true "任务ID"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job/{jobId} [get]
func (c *SysJobController) GetInfo(ctx *gin.Context) {
	jobId, err := strconv.ParseInt(ctx.Param("jobId"), 10, 64)
	if err != nil {
		response.Error(ctx, "任务ID无效")
		return
	}

	job, err := c.jobService.SelectJobById(jobId)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, job)
}

// Add 新增定时任务
// @Summary 新增定时任务
// @Description 新增定时任务
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param job body model.SysJob true "定时任务信息"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job [post]
func (c *SysJobController) Add(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		response.Error(ctx, err.Error())
		return
	}

	// 校验Cron表达式
	if !c.jobService.ValidateCronExpression(job.CronExpression) {
		response.Error(ctx, "Cron表达式无效")
		return
	}

	// 校验调用目标字符串
	if strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_RMI) {
		response.Error(ctx, fmt.Sprintf("新增任务'%s'失败，目标字符串不允许'rmi'调用", job.JobName))
		return
	}

	if strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_LDAP) ||
		strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_LDAPS) {
		response.Error(ctx, fmt.Sprintf("新增任务'%s'失败，目标字符串不允许'ldap(s)'调用", job.JobName))
		return
	}

	// 获取当前用户名
	username, _ := ctx.Get(constants.USERNAME_KEY)
	if username != nil {
		job.CreateBy = username.(string)
	}

	err := c.jobService.InsertJob(&job)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Edit 修改定时任务
// @Summary 修改定时任务
// @Description 修改定时任务
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param job body model.SysJob true "定时任务信息"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job [put]
func (c *SysJobController) Edit(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		response.Error(ctx, err.Error())
		return
	}

	// 校验Cron表达式
	if !c.jobService.ValidateCronExpression(job.CronExpression) {
		response.Error(ctx, "Cron表达式无效")
		return
	}

	// 校验调用目标字符串
	if strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_RMI) {
		response.Error(ctx, fmt.Sprintf("修改任务'%s'失败，目标字符串不允许'rmi'调用", job.JobName))
		return
	}

	if strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_LDAP) ||
		strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_LDAPS) {
		response.Error(ctx, fmt.Sprintf("修改任务'%s'失败，目标字符串不允许'ldap(s)'调用", job.JobName))
		return
	}

	// 获取当前用户名
	username, _ := ctx.Get(constants.USERNAME_KEY)
	if username != nil {
		job.UpdateBy = username.(string)
	}

	err := c.jobService.UpdateJob(&job)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Remove 删除定时任务
// @Summary 删除定时任务
// @Description 删除定时任务
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param jobIds path string true "任务ID，多个以逗号分隔"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job/{jobIds} [delete]
func (c *SysJobController) Remove(ctx *gin.Context) {
	jobIds := ctx.Param("jobIds")
	ids := strings.Split(jobIds, ",")
	idList := make([]int64, 0, len(ids))

	for _, id := range ids {
		jobId, err := strconv.ParseInt(id, 10, 64)
		if err != nil {
			continue
		}
		idList = append(idList, jobId)
	}

	err := c.jobService.DeleteJobByIds(idList)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// ChangeStatus 修改定时任务状态
// @Summary 修改定时任务状态
// @Description 修改定时任务状态
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param job body model.SysJob true "定时任务信息"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job/changeStatus [put]
func (c *SysJobController) ChangeStatus(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		response.Error(ctx, err.Error())
		return
	}

	err := c.jobService.ChangeStatus(&job)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Run 立即执行一次定时任务
// @Summary 立即执行一次定时任务
// @Description 立即执行一次定时任务
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param job body model.SysJob true "定时任务信息"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job/run [put]
func (c *SysJobController) Run(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		response.Error(ctx, err.Error())
		return
	}

	err := c.jobService.Run(&job)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// CheckCronExpressionIsValid 校验Cron表达式是否有效
// @Summary 校验Cron表达式是否有效
// @Description 校验Cron表达式是否有效
// @Tags 定时任务
// @Accept json
// @Produce json
// @Param cronExpression query string true "Cron表达式"
// @Success 200 {object} response.ResponseData
// @Router /monitor/job/checkCronExpressionIsValid [get]
func (c *SysJobController) CheckCronExpressionIsValid(ctx *gin.Context) {
	cronExpression := ctx.Query("cronExpression")
	isValid := c.jobService.ValidateCronExpression(cronExpression)
	response.Success(ctx, isValid)
}
