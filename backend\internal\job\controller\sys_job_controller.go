package controller

import (
	"backend/internal/constants"
	"backend/internal/job/service"
	"backend/internal/model"
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysJobController 定时任务控制器
type SysJobController struct {
	jobService service.SysJobService
}

// NewSysJobController 创建定时任务控制器
func NewSysJobController(jobService service.SysJobService) *SysJobController {
	return &SysJobController{
		jobService: jobService,
	}
}

// List 查询定时任务列表
func (c *SysJobController) List(ctx *gin.Context) {
	// 构建查询参数
	job := &model.SysJob{
		JobName:      ctx.Query("jobName"),
		JobGroup:     ctx.Query("jobGroup"),
		Status:       ctx.Query("status"),
		InvokeTarget: ctx.Query("invokeTarget"),
	}

	// 查询列表
	list, err := c.jobService.SelectJobList(job)
	if err != nil {
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "查询定时任务列表失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code":  200,
		"msg":   "查询成功",
		"rows":  list,
		"total": len(list),
	})
}

// GetInfo 获取定时任务详细信息
func (c *SysJobController) GetInfo(ctx *gin.Context) {
	jobId, err := strconv.ParseInt(ctx.Param("jobId"), 10, 64)
	if err != nil {
		ctx.JSON(400, gin.H{
			"code": 400,
			"msg":  "无效的任务ID",
		})
		return
	}

	job, err := c.jobService.SelectJobById(jobId)
	if err != nil {
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "获取定时任务失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "查询成功",
		"data": job,
	})
}

// Add 新增定时任务
func (c *SysJobController) Add(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		ctx.JSON(400, gin.H{
			"code": 400,
			"msg":  "参数解析失败: " + err.Error(),
		})
		return
	}

	// 校验Cron表达式
	if !c.jobService.CheckCronExpressionIsValid(job.CronExpression) {
		ctx.JSON(400, gin.H{
			"code": 400,
			"msg":  fmt.Sprintf("新增任务'%s'失败，Cron表达式不正确", job.JobName),
		})
		return
	}

	// 校验调用目标字符串
	if strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_RMI) {
		ctx.JSON(400, gin.H{
			"code": 400,
			"msg":  fmt.Sprintf("新增任务'%s'失败，目标字符串不允许'rmi'调用", job.JobName),
		})
		return
	}

	if strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_LDAP) ||
		strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_LDAPS) {
		ctx.JSON(400, gin.H{
			"code": 400,
			"msg":  fmt.Sprintf("新增任务'%s'失败，目标字符串不允许'ldap(s)'调用", job.JobName),
		})
		return
	}

	// 获取当前用户名
	username, _ := ctx.Get(constants.USERNAME_KEY)
	if username != nil {
		job.CreateBy = username.(string)
	}

	rows, err := c.jobService.InsertJob(&job)
	if err != nil {
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "新增定时任务失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "新增成功",
		"data": rows,
	})
}

// Edit 修改定时任务
func (c *SysJobController) Edit(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		ctx.JSON(400, gin.H{
			"code": 400,
			"msg":  "参数解析失败: " + err.Error(),
		})
		return
	}

	// 校验Cron表达式
	if !c.jobService.CheckCronExpressionIsValid(job.CronExpression) {
		ctx.JSON(400, gin.H{
			"code": 400,
			"msg":  fmt.Sprintf("修改任务'%s'失败，Cron表达式不正确", job.JobName),
		})
		return
	}

	// 校验调用目标字符串
	if strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_RMI) {
		ctx.JSON(400, gin.H{
			"code": 400,
			"msg":  fmt.Sprintf("修改任务'%s'失败，目标字符串不允许'rmi'调用", job.JobName),
		})
		return
	}

	if strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_LDAP) ||
		strings.Contains(strings.ToLower(job.InvokeTarget), constants.LOOKUP_LDAPS) {
		ctx.JSON(400, gin.H{
			"code": 400,
			"msg":  fmt.Sprintf("修改任务'%s'失败，目标字符串不允许'ldap(s)'调用", job.JobName),
		})
		return
	}

	// 获取当前用户名
	username, _ := ctx.Get(constants.USERNAME_KEY)
	if username != nil {
		job.UpdateBy = username.(string)
	}

	rows, err := c.jobService.UpdateJob(&job)
	if err != nil {
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "修改定时任务失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "修改成功",
		"data": rows,
	})
}

// ChangeStatus 修改定时任务状态
func (c *SysJobController) ChangeStatus(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		ctx.JSON(400, gin.H{
			"code": 400,
			"msg":  "参数解析失败: " + err.Error(),
		})
		return
	}

	newJob, err := c.jobService.SelectJobById(job.JobID)
	if err != nil {
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "获取任务信息失败: " + err.Error(),
		})
		return
	}

	newJob.Status = job.Status
	rows, err := c.jobService.ChangeStatus(newJob)
	if err != nil {
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "修改任务状态失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "状态修改成功",
		"data": rows,
	})
}

// Run 立即执行一次任务
func (c *SysJobController) Run(ctx *gin.Context) {
	var job model.SysJob
	if err := ctx.ShouldBindJSON(&job); err != nil {
		ctx.JSON(400, gin.H{
			"code": 400,
			"msg":  "参数解析失败: " + err.Error(),
		})
		return
	}

	result, err := c.jobService.Run(&job)
	if err != nil {
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "执行任务失败: " + err.Error(),
		})
		return
	}

	if !result {
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "任务不存在或已过期！",
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "执行成功",
	})
}

// Remove 删除定时任务
func (c *SysJobController) Remove(ctx *gin.Context) {
	jobIdsStr := ctx.Param("jobIds")
	jobIdsArr := strings.Split(jobIdsStr, ",")
	jobIds := make([]int64, 0, len(jobIdsArr))

	for _, idStr := range jobIdsArr {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			ctx.JSON(400, gin.H{
				"code": 400,
				"msg":  "无效的任务ID: " + idStr,
			})
			return
		}
		jobIds = append(jobIds, id)
	}

	err := c.jobService.DeleteJobByIds(jobIds)
	if err != nil {
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "删除任务失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "删除成功",
	})
}
