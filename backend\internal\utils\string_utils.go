package utils

import (
	"math/rand"
	"regexp"
	"strings"
	"time"
)

// init 初始化随机数种子
func init() {
	rand.Seed(time.Now().UnixNano())
}

// IsEmpty 判断字符串是否为空
func IsEmpty(str string) bool {
	return len(strings.TrimSpace(str)) == 0
}

// IsNotEmpty 判断字符串是否不为空
func IsNotEmpty(str string) bool {
	return !IsEmpty(str)
}

// IsBlank 判断字符串是否为空白
func IsBlank(str string) bool {
	return IsEmpty(str) || strings.TrimSpace(str) == ""
}

// IsNotBlank 判断字符串是否不为空白
func IsNotBlank(str string) bool {
	return !IsBlank(str)
}

// IsHttp 判断是否是http(s)链接
func IsHttp(link string) bool {
	if IsEmpty(link) {
		return false
	}
	return strings.HasPrefix(strings.ToLower(link), "http") ||
		strings.HasPrefix(strings.ToLower(link), "https")
}

// Equals 比较两个字符串是否相等
func Equals(str1, str2 string) bool {
	return str1 == str2
}

// TrimSpace 去除字符串前后空格
func TrimSpace(str string) string {
	return strings.TrimSpace(str)
}

// Split 分割字符串
func Split(str, sep string) []string {
	if IsEmpty(str) {
		return []string{}
	}
	return strings.Split(str, sep)
}

// DefaultString 返回非空字符串，如果为空则返回默认值
func DefaultString(str, defaultStr string) string {
	if IsEmpty(str) {
		return defaultStr
	}
	return str
}

// ContainsAny 判断字符串是否包含任意一个字符串
func ContainsAny(str string, searchStrs ...string) bool {
	if IsEmpty(str) || len(searchStrs) == 0 {
		return false
	}
	for _, searchStr := range searchStrs {
		if strings.Contains(str, searchStr) {
			return true
		}
	}
	return false
}

// ContainsAll 判断字符串是否包含所有字符串
func ContainsAll(str string, searchStrs ...string) bool {
	if IsEmpty(str) || len(searchStrs) == 0 {
		return false
	}
	for _, searchStr := range searchStrs {
		if !strings.Contains(str, searchStr) {
			return false
		}
	}
	return true
}

// ToLower 将字符串转换为小写
func ToLower(str string) string {
	if IsEmpty(str) {
		return str
	}
	return strings.ToLower(str)
}

// ToUpper 将字符串转换为大写
func ToUpper(str string) string {
	if IsEmpty(str) {
		return str
	}
	return strings.ToUpper(str)
}

// MatchRegexp 正则匹配
func MatchRegexp(str, pattern string) bool {
	matched, _ := regexp.MatchString(pattern, str)
	return matched
}

// ContainsIgnoreCase 判断字符串是否包含子字符串（忽略大小写）
func ContainsIgnoreCase(str, subStr string) bool {
	return strings.Contains(strings.ToLower(str), strings.ToLower(subStr))
}

// MatchPhone 判断是否为手机号
func MatchPhone(phone string) bool {
	// 简单的手机号验证：1开头的11位数字
	pattern := `^1\d{10}$`
	matched, _ := regexp.MatchString(pattern, phone)
	return matched
}
