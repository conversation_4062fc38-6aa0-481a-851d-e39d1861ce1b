package model

import (
	"strings"
	"time"
)

// GenTableColumn 代码生成业务表字段
type GenTableColumn struct {
	// 编号
	ColumnID int64 `json:"columnId" gorm:"column:column_id;primary_key;auto_increment;comment:编号"`
	// 归属表编号
	TableID int64 `json:"tableId" gorm:"column:table_id;comment:归属表编号"`
	// 列名称
	ColumnName string `json:"columnName" gorm:"column:column_name;comment:列名称"`
	// 列描述
	ColumnComment string `json:"columnComment" gorm:"column:column_comment;comment:列描述"`
	// 列类型
	ColumnType string `json:"columnType" gorm:"column:column_type;comment:列类型"`
	// JAVA类型
	JavaType string `json:"javaType" gorm:"column:java_type;comment:JAVA类型"`
	// JAVA字段名
	JavaField string `json:"javaField" gorm:"column:java_field;comment:JAVA字段名"`
	// 是否主键（1是）
	IsPk string `json:"isPk" gorm:"column:is_pk;comment:是否主键（1是）"`
	// 是否自增（1是）
	IsIncrement string `json:"isIncrement" gorm:"column:is_increment;comment:是否自增（1是）"`
	// 是否必填（1是）
	IsRequired string `json:"isRequired" gorm:"column:is_required;comment:是否必填（1是）"`
	// 是否为插入字段（1是）
	IsInsert string `json:"isInsert" gorm:"column:is_insert;comment:是否为插入字段（1是）"`
	// 是否编辑字段（1是）
	IsEdit string `json:"isEdit" gorm:"column:is_edit;comment:是否编辑字段（1是）"`
	// 是否列表字段（1是）
	IsList string `json:"isList" gorm:"column:is_list;comment:是否列表字段（1是）"`
	// 是否查询字段（1是）
	IsQuery string `json:"isQuery" gorm:"column:is_query;comment:是否查询字段（1是）"`
	// 查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）
	QueryType string `json:"queryType" gorm:"column:query_type;comment:查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）"`
	// 显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）
	HtmlType string `json:"htmlType" gorm:"column:html_type;comment:显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）"`
	// 字典类型
	DictType string `json:"dictType" gorm:"column:dict_type;comment:字典类型"`
	// 排序
	Sort int `json:"sort" gorm:"column:sort;comment:排序"`
	// 创建者
	CreateBy string `json:"createBy" gorm:"column:create_by;comment:创建者"`
	// 创建时间
	CreateTime *time.Time `json:"createTime" gorm:"column:create_time;comment:创建时间"`
	// 更新者
	UpdateBy string `json:"updateBy" gorm:"column:update_by;comment:更新者"`
	// 更新时间
	UpdateTime *time.Time `json:"updateTime" gorm:"column:update_time;comment:更新时间"`
}

// GetTableName 设置表名
func (GenTableColumn) GetTableName() string {
	return "gen_table_column"
}

// GetCapJavaField 获取首字母大写的Java字段
func (c *GenTableColumn) GetCapJavaField() string {
	if c.JavaField == "" {
		return ""
	}
	return strings.ToUpper(c.JavaField[:1]) + c.JavaField[1:]
}

// IsPkColumn 是否是主键
func (c *GenTableColumn) IsPkColumn() bool {
	return c.IsPk == "1"
}

// IsIncrementColumn 是否是自增
func (c *GenTableColumn) IsIncrementColumn() bool {
	return c.IsIncrement == "1"
}

// IsRequiredColumn 是否是必填
func (c *GenTableColumn) IsRequiredColumn() bool {
	return c.IsRequired == "1"
}

// IsInsertColumn 是否是插入字段
func (c *GenTableColumn) IsInsertColumn() bool {
	return c.IsInsert == "1"
}

// IsEditColumn 是否是编辑字段
func (c *GenTableColumn) IsEditColumn() bool {
	return c.IsEdit == "1"
}

// IsListColumn 是否是列表字段
func (c *GenTableColumn) IsListColumn() bool {
	return c.IsList == "1"
}

// IsQueryColumn 是否是查询字段
func (c *GenTableColumn) IsQueryColumn() bool {
	return c.IsQuery == "1"
}

// IsSuperColumn 是否是父类字段
func (c *GenTableColumn) IsSuperColumn() bool {
	// 父类字段包括：创建者、创建时间、更新者、更新时间、备注、删除标志
	return c.JavaField == "createBy" || c.JavaField == "createTime" ||
		c.JavaField == "updateBy" || c.JavaField == "updateTime" ||
		c.JavaField == "remark" || c.JavaField == "delFlag"
}

// IsUsableColumn 是否是可用字段
func (c *GenTableColumn) IsUsableColumn() bool {
	return !c.IsSuperColumn()
}

// ReadConverterExp 读取转换表达式
func (c *GenTableColumn) ReadConverterExp() string {
	if c.DictType == "" {
		return ""
	}
	return c.DictType
}
