package model

import (
	"strings"
	"unicode"
)

// GenTableColumn 代码生成业务字段表 gen_table_column
type GenTableColumn struct {
	BaseEntity
	ColumnId      uint64 `json:"columnId" gorm:"column:column_id;primaryKey;comment:编号"`
	TableId       uint64 `json:"tableId" gorm:"column:table_id;comment:归属表编号"`
	ColumnName    string `json:"columnName" gorm:"column:column_name;type:varchar(200);comment:列名称"`
	ColumnComment string `json:"columnComment" gorm:"column:column_comment;type:varchar(500);comment:列描述"`
	ColumnType    string `json:"columnType" gorm:"column:column_type;type:varchar(100);comment:列类型"`
	JavaType      string `json:"javaType" gorm:"column:java_type;type:varchar(500);comment:JAVA类型"`
	JavaField     string `json:"javaField" gorm:"column:java_field;type:varchar(200);comment:JAVA字段名"`
	IsPk          string `json:"isPk" gorm:"column:is_pk;type:char(1);comment:是否主键（1是）"`
	IsIncrement   string `json:"isIncrement" gorm:"column:is_increment;type:char(1);comment:是否自增（1是）"`
	IsRequired    string `json:"isRequired" gorm:"column:is_required;type:char(1);comment:是否必填（1是）"`
	IsInsert      string `json:"isInsert" gorm:"column:is_insert;type:char(1);comment:是否为插入字段（1是）"`
	IsEdit        string `json:"isEdit" gorm:"column:is_edit;type:char(1);comment:是否编辑字段（1是）"`
	IsList        string `json:"isList" gorm:"column:is_list;type:char(1);comment:是否列表字段（1是）"`
	IsQuery       string `json:"isQuery" gorm:"column:is_query;type:char(1);comment:是否查询字段（1是）"`
	QueryType     string `json:"queryType" gorm:"column:query_type;type:varchar(200);default:EQ;comment:查询方式（等于、不等于、大于、小于、范围）"`
	HtmlType      string `json:"htmlType" gorm:"column:html_type;type:varchar(200);comment:显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）"`
	DictType      string `json:"dictType" gorm:"column:dict_type;type:varchar(200);comment:字典类型"`
	Sort          int    `json:"sort" gorm:"column:sort;comment:排序"`
}

// TableName 设置表名
func (GenTableColumn) TableName() string {
	return "gen_table_column"
}

// GetCapJavaField 获取首字母大写的Java字段名
func (g *GenTableColumn) GetCapJavaField() string {
	if g.JavaField == "" {
		return ""
	}
	runes := []rune(g.JavaField)
	runes[0] = unicode.ToUpper(runes[0])
	return string(runes)
}

// IsPkField 是否为主键字段
func (g *GenTableColumn) IsPkField() bool {
	return g.IsPk == "1"
}

// IsIncrementField 是否为自增字段
func (g *GenTableColumn) IsIncrementField() bool {
	return g.IsIncrement == "1"
}

// IsRequiredField 是否为必填字段
func (g *GenTableColumn) IsRequiredField() bool {
	return g.IsRequired == "1"
}

// IsInsertField 是否为插入字段
func (g *GenTableColumn) IsInsertField() bool {
	return g.IsInsert == "1"
}

// IsEditField 是否为编辑字段
func (g *GenTableColumn) IsEditField() bool {
	return g.IsEdit == "1"
}

// IsListField 是否为列表字段
func (g *GenTableColumn) IsListField() bool {
	return g.IsList == "1"
}

// IsQueryField 是否为查询字段
func (g *GenTableColumn) IsQueryField() bool {
	return g.IsQuery == "1"
}

// IsSuperColumn 是否为基类字段
func (g *GenTableColumn) IsSuperColumn() bool {
	superColumns := []string{
		"createBy", "createTime", "updateBy", "updateTime", "remark",
		"parentName", "parentId", "orderNum", "ancestors",
	}
	
	for _, col := range superColumns {
		if strings.EqualFold(g.JavaField, col) {
			return true
		}
	}
	return false
}

// IsUsableColumn 是否为可用字段
func (g *GenTableColumn) IsUsableColumn() bool {
	usableColumns := []string{"parentId", "orderNum", "remark"}
	
	for _, col := range usableColumns {
		if strings.EqualFold(g.JavaField, col) {
			return true
		}
	}
	return false
}

// ReadConverterExp 读取转换表达式
func (g *GenTableColumn) ReadConverterExp() string {
	comment := g.ColumnComment
	if comment == "" {
		return ""
	}
	
	// 提取括号中的内容
	start := strings.Index(comment, "（")
	end := strings.Index(comment, "）")
	if start == -1 || end == -1 || start >= end {
		return comment
	}
	
	remarks := comment[start+3 : end] // +3 因为中文括号占3个字节
	if remarks == "" {
		return comment
	}
	
	var result []string
	for _, value := range strings.Split(remarks, " ") {
		value = strings.TrimSpace(value)
		if value != "" && len(value) > 1 {
			startStr := string([]rune(value)[0])
			endStr := string([]rune(value)[1:])
			result = append(result, startStr+"="+endStr)
		}
	}
	
	if len(result) > 0 {
		return strings.Join(result, ",")
	}
	return comment
}
