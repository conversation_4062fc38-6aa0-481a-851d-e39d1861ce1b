package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
)

// DeleteFile 删除文件
func DeleteFile(filePath string) bool {
	if filePath == "" {
		return false
	}

	// 检查文件是否存在
	_, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return true
	}

	// 删除文件
	err = os.Remove(filePath)
	if err != nil {
		return false
	}

	return true
}

// GenerateUniqueFileName 生成唯一的文件名
func GenerateUniqueFileName() string {
	// 使用UUID生成唯一标识
	id := uuid.New().String()
	// 去除UUID中的"-"
	id = strings.ReplaceAll(id, "-", "")
	// 添加时间戳前缀
	timestamp := time.Now().Format("20060102150405")
	return fmt.Sprintf("%s_%s", timestamp, id)
}

// GetFileExt 获取文件扩展名
func GetFileExt(fileName string) string {
	return strings.ToLower(filepath.Ext(fileName))
}

// CheckAllowExt 检查文件扩展名是否在允许列表中
func CheckAllowExt(fileName string, allowExts []string) bool {
	ext := GetFileExt(fileName)
	for _, allowExt := range allowExts {
		if strings.ToLower(allowExt) == ext {
			return true
		}
	}
	return false
}

// EnsureDirExists 确保目录存在，如果不存在则创建
func EnsureDirExists(dirPath string) error {
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		return os.MkdirAll(dirPath, os.ModePerm)
	}
	return nil
}
