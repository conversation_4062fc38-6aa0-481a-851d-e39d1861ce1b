package impl

import (
	"backend/internal/job/constants"
	"backend/internal/job/repository"
	"backend/internal/job/service"
	"backend/internal/job/util"
	"backend/internal/model"
	"backend/internal/utils"
	"fmt"
	"sync"
)

// sysJobService 定时任务服务实现
type sysJobService struct {
	jobRepository repository.SysJobRepository
	jobLogService service.SysJobLogService
	tasks         map[int64]*util.JobExecutor
	mu            sync.RWMutex
}

// NewSysJobService 创建任务服务实例
func NewSysJobService(jobRepository repository.SysJobRepository, jobLogService service.SysJobLogService) service.SysJobService {
	return &sysJobService{
		jobRepository: jobRepository,
		jobLogService: jobLogService,
		tasks:         make(map[int64]*util.JobExecutor),
	}
}

// SelectJobList 获取任务列表
func (s *sysJobService) SelectJobList(job *model.SysJob) ([]*model.SysJob, error) {
	return s.jobRepository.SelectJobList(job)
}

// SelectJobById 根据ID获取任务
func (s *sysJobService) SelectJobById(jobId int64) (*model.SysJob, error) {
	return s.jobRepository.SelectJobById(jobId)
}

// PauseJob 暂停任务
func (s *sysJobService) PauseJob(job *model.SysJob) (int, error) {
	job.Status = constants.STATUS_PAUSE
	return s.jobRepository.UpdateJob(job)
}

// ResumeJob 恢复任务
func (s *sysJobService) ResumeJob(job *model.SysJob) (int, error) {
	job.Status = constants.STATUS_NORMAL
	return s.jobRepository.UpdateJob(job)
}

// DeleteJob 删除任务
func (s *sysJobService) DeleteJob(job *model.SysJob) (int, error) {
	s.mu.Lock()
	delete(s.tasks, job.JobID)
	s.mu.Unlock()
	return s.jobRepository.DeleteJob(job.JobID)
}

// DeleteJobByIds 批量删除任务
func (s *sysJobService) DeleteJobByIds(jobIds []int64) error {
	s.mu.Lock()
	for _, jobId := range jobIds {
		delete(s.tasks, jobId)
	}
	s.mu.Unlock()
	_, err := s.jobRepository.DeleteJobByIds(jobIds)
	return err
}

// ChangeStatus 更改任务状态
func (s *sysJobService) ChangeStatus(job *model.SysJob) (int, error) {
	if job.Status == constants.STATUS_NORMAL {
		job.Status = constants.STATUS_PAUSE
	} else if job.Status == constants.STATUS_PAUSE {
		job.Status = constants.STATUS_NORMAL
	}
	return s.jobRepository.UpdateJob(job)
}

// Run 立即执行任务
func (s *sysJobService) Run(job *model.SysJob) (bool, error) {
	if !s.CheckCronExpressionIsValid(job.CronExpression) {
		return false, fmt.Errorf("Cron表达式无效: %s", job.CronExpression)
	}

	// 创建执行器并执行任务
	executor := util.NewJobExecutor(job)
	go func() {
		executor.Execute()
		// 记录日志
		s.jobLogService.AddJobLog(executor.JobLog)
	}()

	return true, nil
}

// InsertJob 新增任务
func (s *sysJobService) InsertJob(job *model.SysJob) (int, error) {
	if !s.CheckCronExpressionIsValid(job.CronExpression) {
		return 0, fmt.Errorf("Cron表达式无效: %s", job.CronExpression)
	}
	return s.jobRepository.InsertJob(job)
}

// UpdateJob 更新任务
func (s *sysJobService) UpdateJob(job *model.SysJob) (int, error) {
	if !s.CheckCronExpressionIsValid(job.CronExpression) {
		return 0, fmt.Errorf("Cron表达式无效: %s", job.CronExpression)
	}
	return s.jobRepository.UpdateJob(job)
}

// CheckCronExpressionIsValid 检查Cron表达式是否有效
func (s *sysJobService) CheckCronExpressionIsValid(cronExpression string) bool {
	return utils.IsValidCronExpression(cronExpression)
}
