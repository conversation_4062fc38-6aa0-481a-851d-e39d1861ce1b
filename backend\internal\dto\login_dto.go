package dto

// LoginRequest 登录请求参数
type LoginRequest struct {
	// 用户名
	Username string `json:"username" binding:"required"`
	// 用户密码
	Password string `json:"password" binding:"required"`
	// 验证码
	Code string `json:"code"`
	// 唯一标识
	UUID string `json:"uuid"`
}

// LoginVO 登录响应结果
type LoginVO struct {
	// 访问令牌
	Token string `json:"token"`
	// 过期时间
	ExpiresIn int64 `json:"expiresIn"`
}

// UserInfoVO 用户信息响应结果
type UserInfoVO struct {
	// 用户信息
	User interface{} `json:"user"`
	// 角色列表
	Roles []string `json:"roles"`
	// 权限列表
	Permissions []string `json:"permissions"`
}
