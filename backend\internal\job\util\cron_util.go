package util

import (
	"backend/internal/constants"
	"backend/internal/model"
	"fmt"
	"time"

	"github.com/robfig/cron/v3"
)

// GetCronExpression 获取cron表达式
func GetCronExpression(job *model.SysJob) string {
	return job.CronExpression
}

// GetInvokeTarget 获取调用目标字符串
func GetInvokeTarget(job *model.SysJob) string {
	return job.InvokeTarget
}

// ParseCronExpression 解析cron表达式
func ParseCronExpression(cronExpression string) (cron.Schedule, error) {
	parser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
	return parser.Parse(cronExpression)
}

// GetNextExecTime 获取下次执行时间
func GetNextExecTime(cronExpression string) (time.Time, error) {
	schedule, err := ParseCronExpression(cronExpression)
	if err != nil {
		return time.Time{}, err
	}
	return schedule.Next(time.Now()), nil
}

// HandleCronError 处理定时任务执行错误
func HandleCronError(job *model.SysJob, err error) {
	// 根据错误策略处理
	switch job.MisfirePolicy {
	case constants.MISFIRE_IGNORE_MISFIRES:
		// 立即执行一次
		fmt.Printf("任务 [%s] 执行错误，立即执行一次\n", job.JobName)
		// 这里可以调用执行任务的方法
	case constants.MISFIRE_FIRE_AND_PROCEED:
		// 执行一次
		fmt.Printf("任务 [%s] 执行错误，执行一次\n", job.JobName)
		// 这里可以调用执行任务的方法
	case constants.MISFIRE_DO_NOTHING:
		// 放弃执行
		fmt.Printf("任务 [%s] 执行错误，放弃执行\n", job.JobName)
	default:
		// 默认策略
		fmt.Printf("任务 [%s] 执行错误，使用默认策略\n", job.JobName)
	}
}
