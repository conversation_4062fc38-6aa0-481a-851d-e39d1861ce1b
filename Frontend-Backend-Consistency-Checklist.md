# 🔍 前后端一致性验证清单

## 🔐 登录认证模块

### 登录接口 POST /login
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **请求参数** | | | | |
| username | String | string | ✅ | 用户名 |
| password | String | string | ✅ | 密码 |
| code | String | string | ✅ | 验证码 |
| uuid | String | string | ✅ | 验证码UUID |
| **响应结构** | | | | |
| code | 200 | 200 | ✅ | 成功状态码 |
| msg | "操作成功" | "操作成功" | ✅ | 成功消息 |
| token | String | string | ✅ | JWT令牌 |
| **错误处理** | | | | |
| 用户名密码错误 | 500 | 500 | ✅ | 错误码一致 |
| 验证码错误 | 500 | 500 | ✅ | 错误码一致 |
| 账号被锁定 | 500 | 500 | ✅ | 错误码一致 |

### 获取用户信息 GET /getInfo
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **请求头** | | | | |
| Authorization | Bearer {token} | Bearer {token} | ✅ | Token格式一致 |
| **响应结构** | | | | |
| code | 200 | 200 | ✅ | 状态码 |
| user | SysUser对象 | SysUser对象 | ✅ | 用户信息 |
| roles | Set<String> | []string | ✅ | 角色列表 |
| permissions | Set<String> | []string | ✅ | 权限列表 |
| **用户对象字段** | | | | |
| userId | Long | int64 | ✅ | 用户ID |
| userName | String | string | ✅ | 用户名 |
| nickName | String | string | ✅ | 昵称 |
| email | String | string | ✅ | 邮箱 |
| phonenumber | String | string | ✅ | 手机号 |
| sex | String | string | ✅ | 性别 |
| avatar | String | string | ✅ | 头像 |
| dept | SysDept | *SysDept | ✅ | 部门信息 |

### 获取路由信息 GET /getRouters
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **响应结构** | | | | |
| code | 200 | 200 | ✅ | 状态码 |
| data | RouterVo[] | []RouterVo | ✅ | 路由数组 |
| **路由对象字段** | | | | |
| name | String | string | ✅ | 路由名称 |
| path | String | string | ✅ | 路由路径 |
| component | String | string | ✅ | 组件路径 |
| meta | MetaVo | MetaVo | ✅ | 路由元信息 |
| children | RouterVo[] | []RouterVo | ✅ | 子路由 |

## 👥 用户管理模块

### 用户列表 GET /system/user/list
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **查询参数** | | | | |
| pageNum | Integer | int | ✅ | 页码 |
| pageSize | Integer | int | ✅ | 每页数量 |
| userName | String | string | ✅ | 用户名 |
| phonenumber | String | string | ✅ | 手机号 |
| status | String | string | ✅ | 状态 |
| deptId | Long | int64 | ✅ | 部门ID |
| **响应结构** | | | | |
| code | 200 | 200 | ✅ | 状态码 |
| msg | "查询成功" | "查询成功" | ✅ | 消息 |
| total | Long | int64 | ✅ | 总记录数 |
| rows | List<SysUser> | []*SysUser | ✅ | 用户列表 |
| **分页信息** | | | | |
| 分页格式 | PageHelper | 自定义分页 | ✅ | 分页结构一致 |

### 新增用户 POST /system/user
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **请求体** | | | | |
| userName | String(必填) | string(必填) | ✅ | 用户名 |
| nickName | String(必填) | string(必填) | ✅ | 昵称 |
| deptId | Long | int64 | ✅ | 部门ID |
| roleIds | Long[] | []int64 | ✅ | 角色ID数组 |
| postIds | Long[] | []int64 | ✅ | 岗位ID数组 |
| email | String | string | ✅ | 邮箱 |
| phonenumber | String | string | ✅ | 手机号 |
| sex | String | string | ✅ | 性别 |
| status | String | string | ✅ | 状态 |
| **数据验证** | | | | |
| 用户名唯一性 | ✅ | ✅ | ✅ | 验证逻辑一致 |
| 邮箱格式验证 | ✅ | ✅ | ✅ | 验证逻辑一致 |
| 手机号格式验证 | ✅ | ✅ | ✅ | 验证逻辑一致 |
| **响应结构** | | | | |
| 成功响应 | toAjax(rows) | ToAjax(rows) | ✅ | 响应格式一致 |

## 🎭 角色管理模块

### 角色列表 GET /system/role/list
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **查询参数** | | | | |
| roleName | String | string | ✅ | 角色名称 |
| roleKey | String | string | ✅ | 权限字符 |
| status | String | string | ✅ | 状态 |
| **响应字段** | | | | |
| roleId | Long | int64 | ✅ | 角色ID |
| roleName | String | string | ✅ | 角色名称 |
| roleKey | String | string | ✅ | 权限字符 |
| roleSort | Integer | int | ✅ | 显示顺序 |
| dataScope | String | string | ✅ | 数据范围 |
| status | String | string | ✅ | 状态 |

### 角色权限分配 PUT /system/role
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **请求体** | | | | |
| roleId | Long | int64 | ✅ | 角色ID |
| menuIds | Long[] | []int64 | ✅ | 菜单ID数组 |
| deptIds | Long[] | []int64 | ✅ | 部门ID数组 |
| menuCheckStrictly | boolean | bool | ✅ | 菜单树选择关联 |
| deptCheckStrictly | boolean | bool | ✅ | 部门树选择关联 |

## 📋 菜单管理模块

### 菜单树结构 GET /system/menu/treeselect
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **响应结构** | | | | |
| id | Long | int64 | ✅ | 菜单ID |
| label | String | string | ✅ | 菜单名称 |
| children | TreeSelect[] | []TreeSelect | ✅ | 子菜单 |
| **树形结构** | | | | |
| 递归构建 | ✅ | ✅ | ✅ | 算法一致 |
| 权限过滤 | ✅ | ✅ | ✅ | 过滤逻辑一致 |

## 🏢 部门管理模块

### 部门树结构 GET /system/dept/treeselect
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **响应结构** | | | | |
| id | Long | int64 | ✅ | 部门ID |
| label | String | string | ✅ | 部门名称 |
| children | TreeSelect[] | []TreeSelect | ✅ | 子部门 |
| **数据权限** | | | | |
| 部门数据权限过滤 | ✅ | ⏳ | 🔄 | 待完善 |

## 📊 监控模块

### 服务器信息 GET /monitor/server
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **系统信息** | | | | |
| computerName | String | string | ✅ | 计算机名 |
| computerIp | String | string | ✅ | IP地址 |
| osName | String | string | ✅ | 操作系统 |
| osArch | String | string | ✅ | 系统架构 |
| **CPU信息** | | | | |
| cpuNum | Integer | int | ✅ | CPU核数 |
| used | Double | float64 | ✅ | CPU使用率 |
| **内存信息** | | | | |
| total | Long | int64 | ✅ | 总内存 |
| used | Long | int64 | ✅ | 已用内存 |
| free | Long | int64 | ✅ | 可用内存 |

### 缓存信息 GET /monitor/cache
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **Redis信息** | | | | |
| version | String | string | ✅ | Redis版本 |
| uptime | String | string | ✅ | 运行时间 |
| connected_clients | String | string | ✅ | 连接数 |
| used_memory | String | string | ✅ | 内存使用 |
| **命令统计** | | | | |
| 命令执行次数 | Map<String,String> | map[string]string | ✅ | 统计信息 |

## 🔧 通用功能

### 验证码 GET /captchaImage
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **响应结构** | | | | |
| uuid | String | string | ✅ | 验证码UUID |
| img | String | string | ✅ | Base64图片 |
| captchaEnabled | boolean | bool | ✅ | 是否启用验证码 |
| **验证码类型** | | | | |
| 数学计算 | ✅ | ✅ | ✅ | 算法一致 |
| 字符验证 | ✅ | ✅ | ✅ | 算法一致 |

### 文件上传 POST /common/upload
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **请求格式** | | | | |
| Content-Type | multipart/form-data | multipart/form-data | ✅ | 格式一致 |
| 文件字段名 | file | file | ✅ | 字段名一致 |
| **响应结构** | | | | |
| fileName | String | string | ✅ | 文件名 |
| newFileName | String | string | ✅ | 新文件名 |
| originalFilename | String | string | ✅ | 原始文件名 |
| url | String | string | ✅ | 访问URL |
| **文件限制** | | | | |
| 文件大小限制 | 10MB | 10MB | ✅ | 限制一致 |
| 文件类型限制 | 图片/文档 | 图片/文档 | ✅ | 类型一致 |

## 🚨 错误处理

### 统一错误格式
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **错误响应结构** | | | | |
| code | Integer | int | ✅ | 错误码 |
| msg | String | string | ✅ | 错误消息 |
| data | null | null | ✅ | 数据为空 |
| **常见错误码** | | | | |
| 成功 | 200 | 200 | ✅ | 操作成功 |
| 失败 | 500 | 500 | ✅ | 操作失败 |
| 未授权 | 401 | 401 | ✅ | 未登录 |
| 禁止访问 | 403 | 403 | ✅ | 无权限 |

## 🔐 权限控制

### Token机制
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **Token格式** | | | | |
| Header名称 | Authorization | Authorization | ✅ | 请求头一致 |
| Token前缀 | Bearer | Bearer | ✅ | 前缀一致 |
| **Token内容** | | | | |
| 用户ID | ✅ | ✅ | ✅ | 包含用户ID |
| 用户名 | ✅ | ✅ | ✅ | 包含用户名 |
| 过期时间 | ✅ | ✅ | ✅ | 30分钟 |
| **Token验证** | | | | |
| 签名验证 | ✅ | ✅ | ✅ | 算法一致 |
| 过期验证 | ✅ | ✅ | ✅ | 逻辑一致 |

### 权限注解
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **权限验证** | | | | |
| @PreAuthorize | @ss.hasPermi | 中间件验证 | ✅ | 验证逻辑一致 |
| 角色验证 | @ss.hasRole | 中间件验证 | ✅ | 验证逻辑一致 |
| 数据权限 | 自定义注解 | 中间件验证 | ⏳ | 待完善 |

## 📊 分页机制

### 分页参数
| 验证项 | Java实现 | Go实现 | 状态 | 备注 |
|--------|----------|--------|------|------|
| **请求参数** | | | | |
| pageNum | Integer | int | ✅ | 页码 |
| pageSize | Integer | int | ✅ | 每页数量 |
| orderByColumn | String | string | ✅ | 排序字段 |
| isAsc | String | string | ✅ | 排序方向 |
| **响应结构** | | | | |
| total | Long | int64 | ✅ | 总记录数 |
| rows | List | []interface{} | ✅ | 数据列表 |
| code | 200 | 200 | ✅ | 状态码 |
| msg | "查询成功" | "查询成功" | ✅ | 消息 |

## ✅ 验收标准

### 🎯 功能完整性
- [ ] 所有Java接口在Go中都有对应实现
- [ ] 所有请求参数格式完全一致
- [ ] 所有响应数据结构完全一致
- [ ] 所有业务逻辑保持一致

### 🔍 数据一致性
- [ ] 数据库字段类型映射正确
- [ ] JSON序列化字段名一致
- [ ] 数据验证规则一致
- [ ] 错误消息格式一致

### 🚀 性能要求
- [ ] 接口响应时间 ≤ Java版本的120%
- [ ] 内存使用 ≤ Java版本的80%
- [ ] 并发处理能力 ≥ Java版本

### 🛡️ 安全要求
- [ ] 认证机制完全一致
- [ ] 权限控制逻辑一致
- [ ] 数据权限过滤一致
- [ ] XSS防护机制一致
