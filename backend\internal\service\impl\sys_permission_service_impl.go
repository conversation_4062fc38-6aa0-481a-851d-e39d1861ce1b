package impl

import (
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"strings"
)

// EmptyPermissionService 权限服务空实现（用于临时替代，后续完善）
type EmptyPermissionService struct{}

// GetRolePermission 获取角色数据权限
func (s *EmptyPermissionService) GetRolePermission(user *model.SysUser) []string {
	return []string{}
}

// GetMenuPermission 获取菜单数据权限
func (s *EmptyPermissionService) GetMenuPermission(user *model.SysUser) []string {
	return []string{}
}

// HasPermission 判断是否有某个权限
func (s *EmptyPermissionService) HasPermission(permissions []string, permission string) bool {
	return true
}

// HasRole 判断是否有某个角色
func (s *EmptyPermissionService) HasRole(role string, roles []string) bool {
	return true
}

// SysPermissionServiceImpl 权限服务实现
type SysPermissionServiceImpl struct {
	roleService service.SysRoleService
	menuService service.SysMenuService
	userRepo    repository.SysUserRepository
}

// NewSysPermissionService 创建权限服务
func NewSysPermissionService(
	roleService service.SysRoleService,
	menuService service.SysMenuService,
	userRepo repository.SysUserRepository,
) service.SysPermissionService {
	return &SysPermissionServiceImpl{
		roleService: roleService,
		menuService: menuService,
		userRepo:    userRepo,
	}
}

// GetRolePermission 获取角色数据权限
func (s *SysPermissionServiceImpl) GetRolePermission(user *model.SysUser) []string {
	roles := make([]string, 0, len(user.Roles))
	for _, role := range user.Roles {
		if model.IsAdminUser(user.UserID) || role.Status == "0" {
			roles = append(roles, role.RoleKey)
		}
	}
	return roles
}

// GetMenuPermission 获取菜单数据权限
func (s *SysPermissionServiceImpl) GetMenuPermission(user *model.SysUser) []string {
	perms := make([]string, 0)
	// 管理员拥有所有权限
	if model.IsAdminUser(user.UserID) {
		perms = append(perms, "*:*:*")
	} else {
		if user.Roles != nil && len(user.Roles) > 0 {
			// TODO: 实现权限查询
		}
	}
	return perms
}

// HasPermission 判断是否有某个权限
func (s *SysPermissionServiceImpl) HasPermission(permissions []string, permission string) bool {
	if len(permissions) == 0 || permission == "" {
		return false
	}

	for _, p := range permissions {
		if p == "*:*:*" {
			return true
		}
		if matchPermission(permission, p) {
			return true
		}
	}
	return false
}

// HasRole 判断是否有某个角色
func (s *SysPermissionServiceImpl) HasRole(role string, roles []string) bool {
	if len(roles) == 0 || role == "" {
		return false
	}

	for _, r := range roles {
		if r == "admin" {
			return true
		}
		if strings.EqualFold(r, role) {
			return true
		}
	}
	return false
}

// matchPermission 匹配权限
func matchPermission(permission, target string) bool {
	// 直接相等
	if permission == target {
		return true
	}

	// 通配符匹配
	permParts := strings.Split(permission, ":")
	targetParts := strings.Split(target, ":")

	if len(permParts) != 3 || len(targetParts) != 3 {
		return false
	}

	// 检查每个部分
	for i := 0; i < 3; i++ {
		if targetParts[i] != "*" && permParts[i] != targetParts[i] {
			return false
		}
	}

	return true
}
