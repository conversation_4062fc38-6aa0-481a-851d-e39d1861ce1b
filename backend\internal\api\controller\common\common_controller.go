package common

import (
	"backend/internal/api/controller"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// CommonController 通用请求处理
type CommonController struct {
	controller.BaseController
	uploadPath   string
	downloadPath string
	resourcePath string
	serverUrl    string
}

// NewCommonController 创建通用控制器
func NewCommonController(uploadPath, downloadPath, resourcePath, serverUrl string) *CommonController {
	return &CommonController{
		uploadPath:   uploadPath,
		downloadPath: downloadPath,
		resourcePath: resourcePath,
		serverUrl:    serverUrl,
	}
}

// FileDownload 通用下载请求
// @Summary 通用下载请求
// @Description 通用下载请求
// @Tags 通用
// @Accept json
// @Produce octet-stream
// @Param fileName query string true "文件名称"
// @Param delete query bool false "是否删除"
// @Success 200 {object} controller.Response "成功"
// @Router /common/download [get]
func (c *CommonController) FileDownload(ctx *gin.Context) {
	fileName := ctx.Query("fileName")
	deleteFlag := ctx.Query("delete") == "true"

	// 检查文件名是否合法
	if !checkAllowDownload(fileName) {
		ctx.String(http.StatusInternalServerError, fmt.Sprintf("文件名称(%s)非法，不允许下载", fileName))
		return
	}

	// 构建文件路径
	realFileName := fmt.Sprintf("%d%s", time.Now().UnixNano(), fileName[strings.Index(fileName, "_")+1:])
	filePath := filepath.Join(c.downloadPath, fileName)

	// 设置响应头
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", realFileName))

	// 发送文件
	ctx.File(filePath)

	// 如果需要删除文件
	if deleteFlag {
		os.Remove(filePath)
	}
}

// UploadFile 通用上传请求（单个）
// @Summary 通用上传请求（单个）
// @Description 通用上传请求（单个）
// @Tags 通用
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "文件"
// @Success 200 {object} controller.Response "成功"
// @Router /common/upload [post]
func (c *CommonController) UploadFile(ctx *gin.Context) {
	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 上传文件
	fileName, err := c.upload(c.uploadPath, file)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 构建URL
	url := c.serverUrl + fileName

	// 构建结果
	result := make(map[string]interface{})
	result["url"] = url
	result["fileName"] = fileName
	result["newFileName"] = filepath.Base(fileName)
	result["originalFilename"] = file.Filename

	c.Success(ctx, result)
}

// UploadFiles 通用上传请求（多个）
// @Summary 通用上传请求（多个）
// @Description 通用上传请求（多个）
// @Tags 通用
// @Accept multipart/form-data
// @Produce json
// @Param files formData file true "文件列表"
// @Success 200 {object} controller.Response "成功"
// @Router /common/uploads [post]
func (c *CommonController) UploadFiles(ctx *gin.Context) {
	// 获取上传的文件
	form, err := ctx.MultipartForm()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.Error(ctx, fmt.Errorf("未找到上传的文件"))
		return
	}

	// 上传文件
	var urls []string
	var fileNames []string
	var newFileNames []string
	var originalFilenames []string

	for _, file := range files {
		fileName, err := c.upload(c.uploadPath, file)
		if err != nil {
			c.Error(ctx, err)
			return
		}

		url := c.serverUrl + fileName
		urls = append(urls, url)
		fileNames = append(fileNames, fileName)
		newFileNames = append(newFileNames, filepath.Base(fileName))
		originalFilenames = append(originalFilenames, file.Filename)
	}

	// 构建结果
	result := make(map[string]interface{})
	result["urls"] = strings.Join(urls, ",")
	result["fileNames"] = strings.Join(fileNames, ",")
	result["newFileNames"] = strings.Join(newFileNames, ",")
	result["originalFilenames"] = strings.Join(originalFilenames, ",")

	c.Success(ctx, result)
}

// ResourceDownload 本地资源通用下载
// @Summary 本地资源通用下载
// @Description 本地资源通用下载
// @Tags 通用
// @Accept json
// @Produce octet-stream
// @Param resource query string true "资源路径"
// @Success 200 {object} controller.Response "成功"
// @Router /common/download/resource [get]
func (c *CommonController) ResourceDownload(ctx *gin.Context) {
	resource := ctx.Query("resource")

	// 检查文件名是否合法
	if !checkAllowDownload(resource) {
		ctx.String(http.StatusInternalServerError, fmt.Sprintf("资源文件(%s)非法，不允许下载", resource))
		return
	}

	// 构建文件路径
	downloadPath := filepath.Join(c.resourcePath, stripPrefix(resource))
	downloadName := filepath.Base(downloadPath)

	// 设置响应头
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", downloadName))

	// 发送文件
	ctx.File(downloadPath)
}

// upload 上传文件
func (c *CommonController) upload(basePath string, file *multipart.FileHeader) (string, error) {
	// 创建目录
	uploadDir := filepath.Join(basePath, time.Now().Format("20060102"))
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		return "", err
	}

	// 生成文件名
	ext := path.Ext(file.Filename)
	fileName := strings.TrimSuffix(file.Filename, ext)
	fileName = strings.ReplaceAll(fileName, " ", "_")
	fileName = uuid.New().String() + "_" + fileName + ext

	// 保存文件
	dst := filepath.Join(uploadDir, fileName)
	if err := saveUploadedFile(file, dst); err != nil {
		return "", err
	}

	// 返回相对路径
	return filepath.Join("/", filepath.Base(basePath), time.Now().Format("20060102"), fileName), nil
}

// saveUploadedFile 保存上传的文件
func saveUploadedFile(file *multipart.FileHeader, dst string) error {
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	_, err = io.Copy(out, src)
	return err
}

// checkAllowDownload 检查文件是否允许下载
func checkAllowDownload(name string) bool {
	// 文件名为空，不允许下载
	if name == "" {
		return false
	}

	// 检查是否包含非法路径
	if strings.Contains(name, "..") || strings.Contains(name, "\\") {
		return false
	}

	// 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(name))
	return ext != ".bat" && ext != ".exe" && ext != ".sh" && ext != ".cmd"
}

// stripPrefix 去除前缀
func stripPrefix(name string) string {
	if strings.HasPrefix(name, "/profile") {
		return strings.TrimPrefix(name, "/profile")
	}
	return name
}
