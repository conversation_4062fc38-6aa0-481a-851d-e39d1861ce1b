package impl

import (
	"strings"

	"backend/internal/constants"
	"backend/internal/dto"
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"backend/internal/utils"
)

// SysMenuServiceImpl 菜单业务实现
type SysMenuServiceImpl struct {
	menuRepository     repository.SysMenuRepository
	roleRepository     repository.SysRoleRepository
	roleMenuRepository repository.SysRoleMenuRepository
}

// NewSysMenuService 创建菜单服务实例
func NewSysMenuService(
	menuRepository repository.SysMenuRepository,
	roleRepository repository.SysRoleRepository,
	roleMenuRepository repository.SysRoleMenuRepository,
) service.SysMenuService {
	return &SysMenuServiceImpl{
		menuRepository:     menuRepository,
		roleRepository:     roleRepository,
		roleMenuRepository: roleMenuRepository,
	}
}

// SelectMenuList 根据用户查询系统菜单列表
func (s *SysMenuServiceImpl) SelectMenuList(userId int64) ([]*model.SysMenu, error) {
	return s.SelectMenuListWithCondition(&model.SysMenu{}, userId)
}

// SelectMenuListWithCondition 根据用户和条件查询系统菜单列表
func (s *SysMenuServiceImpl) SelectMenuListWithCondition(menu *model.SysMenu, userId int64) ([]*model.SysMenu, error) {
	var menuList []*model.SysMenu
	var err error

	// 管理员显示所有菜单信息
	if model.IsAdminUser(userId) {
		menuList, err = s.menuRepository.SelectMenuList(menu)
	} else {
		menuList, err = s.menuRepository.SelectMenuListByUserId(menu, userId)
	}

	return menuList, err
}

// SelectMenuPermsByUserId 根据用户ID查询权限
func (s *SysMenuServiceImpl) SelectMenuPermsByUserId(userId int64) ([]string, error) {
	perms, err := s.menuRepository.SelectMenuPermsByUserId(userId)
	if err != nil {
		return nil, err
	}

	// 将权限字符串分割并去重
	permSet := make(map[string]struct{})
	for _, perm := range perms {
		if perm != "" {
			permArr := strings.Split(strings.TrimSpace(perm), ",")
			for _, p := range permArr {
				permSet[p] = struct{}{}
			}
		}
	}

	// 将集合转为数组
	result := make([]string, 0, len(permSet))
	for p := range permSet {
		result = append(result, p)
	}

	return result, nil
}

// SelectMenuPermsByRoleId 根据角色ID查询权限
func (s *SysMenuServiceImpl) SelectMenuPermsByRoleId(roleId int64) ([]string, error) {
	perms, err := s.menuRepository.SelectMenuPermsByRoleId(roleId)
	if err != nil {
		return nil, err
	}

	// 将权限字符串分割并去重
	permSet := make(map[string]struct{})
	for _, perm := range perms {
		if perm != "" {
			permArr := strings.Split(strings.TrimSpace(perm), ",")
			for _, p := range permArr {
				permSet[p] = struct{}{}
			}
		}
	}

	// 将集合转为数组
	result := make([]string, 0, len(permSet))
	for p := range permSet {
		result = append(result, p)
	}

	return result, nil
}

// SelectMenuTreeByUserId 根据用户ID查询菜单树信息
func (s *SysMenuServiceImpl) SelectMenuTreeByUserId(userId int64) ([]*model.SysMenu, error) {
	var menus []*model.SysMenu
	var err error

	if utils.IsAdmin(userId) {
		menus, err = s.menuRepository.SelectMenuTreeAll()
	} else {
		menus, err = s.menuRepository.SelectMenuTreeByUserId(userId)
	}

	if err != nil {
		return nil, err
	}

	return s.getChildPerms(menus, 0), nil
}

// SelectMenuListByRoleId 根据角色ID查询菜单树信息
func (s *SysMenuServiceImpl) SelectMenuListByRoleId(roleId int64) ([]int64, error) {
	role, err := s.roleRepository.SelectRoleById(roleId)
	if err != nil {
		return nil, err
	}

	return s.menuRepository.SelectMenuListByRoleId(roleId, role.MenuCheckStrictly)
}

// BuildMenus 构建前端路由所需要的菜单
func (s *SysMenuServiceImpl) BuildMenus(menus []*model.SysMenu) ([]*dto.RouterVo, error) {
	routers := make([]*dto.RouterVo, 0, len(menus))

	for _, menu := range menus {
		router := &dto.RouterVo{
			Hidden:    menu.Visible == "1",
			Name:      s.getRouteName(menu),
			Path:      s.getRouterPath(menu),
			Component: s.getComponent(menu),
			Query:     menu.Query,
		}

		// 设置meta信息
		router.Meta = &dto.MetaVo{
			Title:   menu.MenuName,
			Icon:    menu.Icon,
			NoCache: menu.IsCache != "1",
		}

		// 设置链接地址
		if utils.IsHttp(menu.Path) {
			router.Meta.Link = menu.Path
		}

		cMenus := menu.Children
		if len(cMenus) > 0 && menu.MenuType == constants.TYPE_DIR {
			router.AlwaysShow = true
			router.Redirect = "noRedirect"

			childRouters, err := s.BuildMenus(cMenus)
			if err != nil {
				return nil, err
			}
			router.Children = childRouters
		} else if s.isMenuFrame(menu) {
			router.Meta = nil
			childList := make([]*dto.RouterVo, 0, 1)

			children := &dto.RouterVo{
				Path:      menu.Path,
				Component: menu.Component,
				Name:      s.getRouteNameByPath(menu.Path),
				Meta: &dto.MetaVo{
					Title:   menu.MenuName,
					Icon:    menu.Icon,
					NoCache: menu.IsCache != "1",
				},
				Query: menu.Query,
			}

			childList = append(childList, children)
			router.Children = childList
		} else if menu.ParentID == 0 && s.isInnerLink(menu) {
			router.Meta = &dto.MetaVo{
				Title: menu.MenuName,
				Icon:  menu.Icon,
			}
			router.Path = "/"

			childList := make([]*dto.RouterVo, 0, 1)
			children := &dto.RouterVo{
				Path:      s.innerLinkReplaceEach(menu.Path),
				Component: "InnerLink",
				Name:      s.getRouteNameByPath(menu.Path),
				Meta: &dto.MetaVo{
					Title:   menu.MenuName,
					Icon:    menu.Icon,
					NoCache: true,
				},
			}
			childList = append(childList, children)
			router.Children = childList
		}

		routers = append(routers, router)
	}

	return routers, nil
}

// BuildMenuTree 构建前端所需要树结构
func (s *SysMenuServiceImpl) BuildMenuTree(menus []*model.SysMenu) ([]*model.SysMenu, error) {
	// 根据父节点ID进行分组
	menuMap := make(map[int64][]*model.SysMenu)
	for _, m := range menus {
		menuMap[m.ParentID] = append(menuMap[m.ParentID], m)
	}

	// 使用0作为根节点ID
	return s.getChildListFromMap(menuMap, 0), nil
}

// BuildMenuTreeSelect 构建前端所需要下拉树结构
func (s *SysMenuServiceImpl) BuildMenuTreeSelect(menus []*model.SysMenu) ([]*dto.TreeSelect, error) {
	menuTree, err := s.BuildMenuTree(menus)
	if err != nil {
		return nil, err
	}

	treeSelects := make([]*dto.TreeSelect, 0, len(menuTree))
	for _, menu := range menuTree {
		treeSelects = append(treeSelects, dto.NewMenuTreeSelect(menu))
	}

	return treeSelects, nil
}

// SelectMenuById 根据菜单ID查询信息
func (s *SysMenuServiceImpl) SelectMenuById(menuId int64) (*model.SysMenu, error) {
	return s.menuRepository.SelectMenuById(menuId)
}

// HasChildByMenuId 是否存在菜单子节点
func (s *SysMenuServiceImpl) HasChildByMenuId(menuId int64) (bool, error) {
	count, err := s.menuRepository.HasChildByMenuId(menuId)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// CheckMenuExistRole 查询菜单是否存在角色
func (s *SysMenuServiceImpl) CheckMenuExistRole(menuId int64) (bool, error) {
	count, err := s.roleMenuRepository.CheckMenuExistRole(menuId)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// InsertMenu 新增保存菜单信息
func (s *SysMenuServiceImpl) InsertMenu(menu *model.SysMenu) (int64, error) {
	return s.menuRepository.InsertMenu(menu)
}

// UpdateMenu 修改保存菜单信息
func (s *SysMenuServiceImpl) UpdateMenu(menu *model.SysMenu) (int64, error) {
	return s.menuRepository.UpdateMenu(menu)
}

// DeleteMenuById 删除菜单管理信息
func (s *SysMenuServiceImpl) DeleteMenuById(menuId int64) (int64, error) {
	return s.menuRepository.DeleteMenuById(menuId)
}

// CheckMenuNameUnique 校验菜单名称是否唯一
func (s *SysMenuServiceImpl) CheckMenuNameUnique(menu *model.SysMenu) (bool, error) {
	if menu.MenuID != 0 {
		info, err := s.menuRepository.CheckMenuNameUnique(menu.MenuName, menu.ParentID)
		if err != nil {
			return false, err
		}

		if info != nil && info.MenuID != menu.MenuID {
			return false, nil
		}
	} else {
		info, err := s.menuRepository.CheckMenuNameUnique(menu.MenuName, menu.ParentID)
		if err != nil {
			return false, err
		}

		if info != nil {
			return false, nil
		}
	}

	return true, nil
}

// getRouteName 获取路由名称
func (s *SysMenuServiceImpl) getRouteName(menu *model.SysMenu) string {
	if menu == nil {
		return ""
	}

	routerName := menu.Path
	// 非外链并且是一级目录（类型为目录）
	if menu.ParentID == 0 && menu.MenuType == constants.TYPE_DIR && menu.IsFrame == "1" {
		routerName = ""
	}
	return routerName
}

// getRouteNameByPath 获取路由名称（通过路径）
func (s *SysMenuServiceImpl) getRouteNameByPath(path string) string {
	return path
}

// getRouterPath 获取路由地址
func (s *SysMenuServiceImpl) getRouterPath(menu *model.SysMenu) string {
	routerPath := menu.Path
	// 内链打开外网方式
	if menu.ParentID == 0 && menu.MenuType == constants.TYPE_DIR && menu.IsFrame == "1" && !utils.IsHttp(routerPath) {
		routerPath = "/" + menu.Path
	} else if menu.ParentID == 0 && menu.MenuType == constants.TYPE_DIR && menu.IsFrame == "1" && utils.IsHttp(routerPath) {
		routerPath = "/"
	}
	// 非外链并且是一级目录（类型为菜单）
	if menu.ParentID == 0 && menu.MenuType == constants.TYPE_MENU && menu.IsFrame == "1" && !utils.IsHttp(routerPath) {
		routerPath = "/" + routerPath
	}
	return routerPath
}

// getComponent 获取组件信息
func (s *SysMenuServiceImpl) getComponent(menu *model.SysMenu) string {
	component := menu.Component
	if component == "" && menu.ParentID != 0 && menu.MenuType == constants.TYPE_DIR {
		component = "ParentView"
	} else if component == "" && s.isInnerLink(menu) {
		component = "InnerLink"
	} else if component == "" && menu.MenuType != constants.TYPE_BUTTON {
		component = "Layout"
	}
	return component
}

// isMenuFrame 是否为菜单内部跳转
func (s *SysMenuServiceImpl) isMenuFrame(menu *model.SysMenu) bool {
	return menu.ParentID == 0 && constants.TYPE_MENU == menu.MenuType && menu.IsFrame == "1"
}

// isInnerLink 是否为内链组件
func (s *SysMenuServiceImpl) isInnerLink(menu *model.SysMenu) bool {
	return menu.IsFrame == "1" && utils.IsHttp(menu.Path)
}

// isParentView 是否为parent_view组件
func (s *SysMenuServiceImpl) isParentView(menu *model.SysMenu) bool {
	return menu.ParentID != 0 && constants.TYPE_DIR == menu.MenuType
}

// getChildPerms 根据父节点的ID获取所有子节点
func (s *SysMenuServiceImpl) getChildPerms(list []*model.SysMenu, parentId int64) []*model.SysMenu {
	result := make([]*model.SysMenu, 0)
	for _, menu := range list {
		// 如果是根节点, 遍历该父节点的所有子节点
		if menu.ParentID == parentId {
			s.recursionFn(list, menu)
			result = append(result, menu)
		}
	}
	return result
}

// recursionFn 递归列表
func (s *SysMenuServiceImpl) recursionFn(list []*model.SysMenu, t *model.SysMenu) {
	// 得到子节点列表
	childList := s.getChildList(list, t.MenuID)
	t.Children = childList

	for _, child := range childList {
		if s.hasChild(list, child) {
			s.recursionFn(list, child)
		}
	}
}

// getChildList 获取子节点列表
func (s *SysMenuServiceImpl) getChildList(list []*model.SysMenu, parentId int64) []*model.SysMenu {
	result := make([]*model.SysMenu, 0)
	for _, menu := range list {
		if menu.ParentID == parentId {
			result = append(result, menu)
		}
	}
	return result
}

// getChildListFromMap 从map中获取子节点列表
func (s *SysMenuServiceImpl) getChildListFromMap(menuMap map[int64][]*model.SysMenu, parentId int64) []*model.SysMenu {
	result := menuMap[parentId]
	if result == nil {
		return make([]*model.SysMenu, 0)
	}

	for _, menu := range result {
		children := s.getChildListFromMap(menuMap, menu.MenuID)
		menu.Children = children
	}

	return result
}

// hasChild 判断是否有子节点
func (s *SysMenuServiceImpl) hasChild(list []*model.SysMenu, menu *model.SysMenu) bool {
	for _, m := range list {
		if m.ParentID == menu.MenuID {
			return true
		}
	}
	return false
}

// innerLinkReplaceEach 内链域名特殊字符替换
func (s *SysMenuServiceImpl) innerLinkReplaceEach(path string) string {
	return "/inner-link/" + strings.ReplaceAll(path, ":", "/")
}
