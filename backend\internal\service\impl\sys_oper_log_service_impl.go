package impl

import (
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
)

// SysOperLogServiceImpl 操作日志服务实现
type SysOperLogServiceImpl struct {
	operLogRepository repository.SysOperLogRepository
}

// NewSysOperLogService 创建操作日志服务实例
func NewSysOperLogService(operLogRepository repository.SysOperLogRepository) service.SysOperLogService {
	return &SysOperLogServiceImpl{
		operLogRepository: operLogRepository,
	}
}

// InsertOperLog 新增操作日志
func (s *SysOperLogServiceImpl) InsertOperLog(operLog *model.SysOperLog) error {
	return s.operLogRepository.Insert(operLog)
}

// SelectOperLogList 查询系统操作日志集合
func (s *SysOperLogServiceImpl) SelectOperLogList(operLog *model.SysOperLog) ([]*model.SysOperLog, error) {
	return s.operLogRepository.SelectList(operLog)
}

// DeleteOperLogByIds 批量删除系统操作日志
func (s *SysOperLogServiceImpl) DeleteOperLogByIds(operIds []int64) (int64, error) {
	return s.operLogRepository.DeleteByIds(operIds)
}

// SelectOperLogById 查询操作日志详细
func (s *SysOperLogServiceImpl) SelectOperLogById(operId int64) (*model.SysOperLog, error) {
	return s.operLogRepository.SelectById(operId)
}

// CleanOperLog 清空操作日志
func (s *SysOperLogServiceImpl) CleanOperLog() error {
	return s.operLogRepository.DeleteAll()
}
