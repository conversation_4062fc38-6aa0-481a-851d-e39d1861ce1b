package utils

import (
	"backend/internal/model"
)

// IsAdmin 判断是否为管理员
func IsAdmin(userId int64) bool {
	return userId != 0 && userId == 1
}

// GetUserId 获取用户ID
func GetUserId(user *model.LoginUser) int64 {
	if user == nil || user.User == nil {
		return 0
	}
	return user.User.UserID
}

// GetUsername 获取用户名
func GetUsername(user *model.LoginUser) string {
	if user == nil || user.User == nil {
		return ""
	}
	return user.User.UserName
}

// GetLoginUser 获取登录用户
func GetLoginUser() *model.LoginUser {
	// TODO: 从上下文中获取登录用户
	return nil
}

// HasPermission 判断是否有权限
func HasPermission(permission string) bool {
	// TODO: 判断当前用户是否有指定权限
	return false
}

// HasRole 判断是否有角色
func HasRole(role string) bool {
	// TODO: 判断当前用户是否有指定角色
	return false
}
