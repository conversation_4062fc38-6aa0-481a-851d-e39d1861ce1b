# Java实体类到Go结构体映射表

## 👤 SysUser 用户实体

| Java字段 | Java类型 | Go字段 | Go类型 | SQL Server字段 | SQL类型 | 说明 |
|---------|----------|--------|--------|---------------|---------|------|
| userId | Long | UserID | int64 | user_id | bigint IDENTITY(100,1) | 用户ID，主键自增 |
| deptId | Long | DeptID | int64 | dept_id | bigint | 部门ID |
| userName | String | UserName | string | user_name | nvarchar(30) | 用户账号 |
| nickName | String | NickName | string | nick_name | nvarchar(30) | 用户昵称 |
| userType | String | UserType | string | user_type | nvarchar(2) | 用户类型（00系统用户） |
| email | String | Email | string | email | nvarchar(50) | 用户邮箱 |
| phonenumber | String | PhoneNumber | string | phonenumber | nvarchar(11) | 手机号码 |
| sex | String | Sex | string | sex | nchar(1) | 用户性别（0男 1女 2未知） |
| avatar | String | Avatar | string | avatar | nvarchar(100) | 头像地址 |
| password | String | Password | string | password | nvarchar(100) | 密码 |
| status | String | Status | string | status | nchar(1) | 帐号状态（0正常 1停用） |
| delFlag | String | DelFlag | string | del_flag | nchar(1) | 删除标志（0存在 2删除） |
| loginIp | String | LoginIP | string | login_ip | nvarchar(128) | 最后登录IP |
| loginDate | Date | LoginDate | *time.Time | login_date | datetime2(7) | 最后登录时间 |
| pwdUpdateDate | Date | PwdUpdateDate | *time.Time | pwd_update_date | datetime2(7) | 密码最后更新时间 |
| dept | SysDept | Dept | *SysDept | - | - | 部门对象（关联） |
| roles | List<SysRole> | Roles | []*SysRole | - | - | 角色对象（关联） |
| roleIds | Long[] | RoleIDs | []int64 | - | - | 角色组（非数据库字段） |
| postIds | Long[] | PostIDs | []int64 | - | - | 岗位组（非数据库字段） |

## 🎭 SysRole 角色实体

| Java字段 | Java类型 | Go字段 | Go类型 | SQL Server字段 | SQL类型 | 说明 |
|---------|----------|--------|--------|---------------|---------|------|
| roleId | Long | RoleID | int64 | role_id | bigint IDENTITY(1,1) | 角色ID，主键自增 |
| roleName | String | RoleName | string | role_name | nvarchar(30) | 角色名称 |
| roleKey | String | RoleKey | string | role_key | nvarchar(100) | 角色权限字符串 |
| roleSort | Integer | RoleSort | int | role_sort | int | 显示顺序 |
| dataScope | String | DataScope | string | data_scope | nchar(1) | 数据范围（1全部 2自定义 3本部门 4本部门及以下 5本人） |
| menuCheckStrictly | boolean | MenuCheckStrictly | bool | menu_check_strictly | tinyint | 菜单树选择项是否关联显示 |
| deptCheckStrictly | boolean | DeptCheckStrictly | bool | dept_check_strictly | tinyint | 部门树选择项是否关联显示 |
| status | String | Status | string | status | nchar(1) | 角色状态（0正常 1停用） |
| delFlag | String | DelFlag | string | del_flag | nchar(1) | 删除标志（0存在 2删除） |
| menuIds | Long[] | MenuIDs | []int64 | - | - | 菜单组（非数据库字段） |
| deptIds | Long[] | DeptIDs | []int64 | - | - | 部门组（非数据库字段） |

## 📋 SysMenu 菜单实体

| Java字段 | Java类型 | Go字段 | Go类型 | SQL Server字段 | SQL类型 | 说明 |
|---------|----------|--------|--------|---------------|---------|------|
| menuId | Long | MenuID | int64 | menu_id | bigint IDENTITY(2000,1) | 菜单ID，主键自增 |
| menuName | String | MenuName | string | menu_name | nvarchar(50) | 菜单名称 |
| parentId | Long | ParentID | int64 | parent_id | bigint | 父菜单ID |
| orderNum | Integer | OrderNum | int | order_num | int | 显示顺序 |
| path | String | Path | string | path | nvarchar(200) | 路由地址 |
| component | String | Component | string | component | nvarchar(255) | 组件路径 |
| query | String | Query | string | query | nvarchar(255) | 路由参数 |
| routeName | String | RouteName | string | route_name | nvarchar(50) | 路由名称 |
| isFrame | String | IsFrame | string | is_frame | nchar(1) | 是否为外链（0是 1否） |
| isCache | String | IsCache | string | is_cache | nchar(1) | 是否缓存（0缓存 1不缓存） |
| menuType | String | MenuType | string | menu_type | nchar(1) | 菜单类型（M目录 C菜单 F按钮） |
| visible | String | Visible | string | visible | nchar(1) | 菜单状态（0显示 1隐藏） |
| status | String | Status | string | status | nchar(1) | 菜单状态（0正常 1停用） |
| perms | String | Perms | string | perms | nvarchar(100) | 权限标识 |
| icon | String | Icon | string | icon | nvarchar(100) | 菜单图标 |
| children | List<SysMenu> | Children | []*SysMenu | - | - | 子部门（非数据库字段） |

## 🏢 SysDept 部门实体

| Java字段 | Java类型 | Go字段 | Go类型 | SQL Server字段 | SQL类型 | 说明 |
|---------|----------|--------|--------|---------------|---------|------|
| deptId | Long | DeptID | int64 | dept_id | bigint IDENTITY(200,1) | 部门ID，主键自增 |
| parentId | Long | ParentID | int64 | parent_id | bigint | 父部门ID |
| ancestors | String | Ancestors | string | ancestors | nvarchar(50) | 祖级列表 |
| deptName | String | DeptName | string | dept_name | nvarchar(30) | 部门名称 |
| orderNum | Integer | OrderNum | int | order_num | int | 显示顺序 |
| leader | String | Leader | string | leader | nvarchar(20) | 负责人 |
| phone | String | Phone | string | phone | nvarchar(11) | 联系电话 |
| email | String | Email | string | email | nvarchar(50) | 邮箱 |
| status | String | Status | string | status | nchar(1) | 部门状态（0正常 1停用） |
| delFlag | String | DelFlag | string | del_flag | nchar(1) | 删除标志（0存在 2删除） |
| children | List<SysDept> | Children | []*SysDept | - | - | 子部门（非数据库字段） |

## 💼 SysPost 岗位实体

| Java字段 | Java类型 | Go字段 | Go类型 | SQL Server字段 | SQL类型 | 说明 |
|---------|----------|--------|--------|---------------|---------|------|
| postId | Long | PostID | int64 | post_id | bigint IDENTITY(1,1) | 岗位ID，主键自增 |
| postCode | String | PostCode | string | post_code | nvarchar(64) | 岗位编码 |
| postName | String | PostName | string | post_name | nvarchar(50) | 岗位名称 |
| postSort | Integer | PostSort | int | post_sort | int | 显示顺序 |
| status | String | Status | string | status | nchar(1) | 状态（0正常 1停用） |

## ⚙️ SysConfig 参数配置实体

| Java字段 | Java类型 | Go字段 | Go类型 | SQL Server字段 | SQL类型 | 说明 |
|---------|----------|--------|--------|---------------|---------|------|
| configId | Long | ConfigID | int64 | config_id | int IDENTITY(1,1) | 参数主键 |
| configName | String | ConfigName | string | config_name | nvarchar(100) | 参数名称 |
| configKey | String | ConfigKey | string | config_key | nvarchar(100) | 参数键名 |
| configValue | String | ConfigValue | string | config_value | nvarchar(500) | 参数键值 |
| configType | String | ConfigType | string | config_type | nchar(1) | 系统内置（Y是 N否） |

## 📢 SysNotice 通知公告实体

| Java字段 | Java类型 | Go字段 | Go类型 | SQL Server字段 | SQL类型 | 说明 |
|---------|----------|--------|--------|---------------|---------|------|
| noticeId | Integer | NoticeID | int | notice_id | int IDENTITY(1,1) | 公告ID |
| noticeTitle | String | NoticeTitle | string | notice_title | nvarchar(50) | 公告标题 |
| noticeType | String | NoticeType | string | notice_type | nchar(1) | 公告类型（1通知 2公告） |
| noticeContent | String | NoticeContent | string | notice_content | nvarchar(max) | 公告内容 |
| status | String | Status | string | status | nchar(1) | 公告状态（0正常 1关闭） |

## 📊 SysOperLog 操作日志实体

| Java字段 | Java类型 | Go字段 | Go类型 | SQL Server字段 | SQL类型 | 说明 |
|---------|----------|--------|--------|---------------|---------|------|
| operId | Long | OperID | int64 | oper_id | bigint IDENTITY(1,1) | 日志主键 |
| title | String | Title | string | title | nvarchar(50) | 模块标题 |
| businessType | Integer | BusinessType | int | business_type | int | 业务类型（0其它 1新增 2修改 3删除） |
| method | String | Method | string | method | nvarchar(100) | 方法名称 |
| requestMethod | String | RequestMethod | string | request_method | nvarchar(10) | 请求方式 |
| operatorType | Integer | OperatorType | int | operator_type | int | 操作类别（0其它 1后台用户 2手机端用户） |
| operName | String | OperName | string | oper_name | nvarchar(50) | 操作人员 |
| deptName | String | DeptName | string | dept_name | nvarchar(50) | 部门名称 |
| operUrl | String | OperURL | string | oper_url | nvarchar(255) | 请求URL |
| operIp | String | OperIP | string | oper_ip | nvarchar(128) | 主机地址 |
| operLocation | String | OperLocation | string | oper_location | nvarchar(255) | 操作地点 |
| operParam | String | OperParam | string | oper_param | nvarchar(max) | 请求参数 |
| jsonResult | String | JSONResult | string | json_result | nvarchar(max) | 返回参数 |
| status | Integer | Status | int | status | int | 操作状态（0正常 1异常） |
| errorMsg | String | ErrorMsg | string | error_msg | nvarchar(max) | 错误消息 |
| operTime | Date | OperTime | time.Time | oper_time | datetime2(7) | 操作时间 |
| costTime | Long | CostTime | int64 | cost_time | bigint | 消耗时间 |

## 🔐 SysLogininfor 系统访问记录

| Java字段 | Java类型 | Go字段 | Go类型 | SQL Server字段 | SQL类型 | 说明 |
|---------|----------|--------|--------|---------------|---------|------|
| infoId | Long | InfoID | int64 | info_id | bigint IDENTITY(1,1) | 访问ID |
| userName | String | UserName | string | user_name | nvarchar(50) | 用户账号 |
| ipaddr | String | IPAddr | string | ipaddr | nvarchar(128) | 登录IP地址 |
| loginLocation | String | LoginLocation | string | login_location | nvarchar(255) | 登录地点 |
| browser | String | Browser | string | browser | nvarchar(50) | 浏览器类型 |
| os | String | OS | string | os | nvarchar(50) | 操作系统 |
| status | String | Status | string | status | nchar(1) | 登录状态（0成功 1失败） |
| msg | String | Msg | string | msg | nvarchar(255) | 提示消息 |
| loginTime | Date | LoginTime | time.Time | login_time | datetime2(7) | 访问时间 |

## 📝 类型映射规则

### Java到Go类型映射
- `Long` → `int64`
- `Integer` → `int`
- `String` → `string`
- `Date` → `time.Time` 或 `*time.Time`
- `boolean` → `bool`
- `List<T>` → `[]T` 或 `[]*T`
- `Object` → `interface{}`

### SQL Server数据类型
- `bigint` → Go `int64`
- `int` → Go `int`
- `nvarchar(n)` → Go `string`
- `nchar(n)` → Go `string`
- `datetime2(7)` → Go `time.Time`
- `tinyint` → Go `bool` (0/1)
- `nvarchar(max)` → Go `string`

### GORM标签规范
```go
type SysUser struct {
    UserID int64 `json:"userId" gorm:"column:user_id;primary_key;auto_increment;comment:用户ID"`
    UserName string `json:"userName" gorm:"column:user_name;not null;comment:用户账号"`
}
```
