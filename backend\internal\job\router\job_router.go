package router

import (
	"backend/internal/api/middleware"
	"backend/internal/job/controller"
	"backend/internal/job/repository"
	jobImpl "backend/internal/job/service/impl"
	sysImpl "backend/internal/service/impl"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// RegisterJobRouter 注册任务调度相关路由
func RegisterJobRouter(r *gin.Engine, db *gorm.DB) {
	// 创建仓库
	jobRepo := repository.NewSysJobRepository(db)
	jobLogRepo := repository.NewSysJobLogRepository(db)

	// 创建服务
	jobLogService := jobImpl.NewSysJobLogService(jobLogRepo)
	jobService := jobImpl.NewSysJobService(jobRepo, jobLogService)

	// 创建控制器
	jobController := controller.NewSysJobController(jobService)
	jobLogController := controller.NewSysJobLogController(jobLogService)

	// 创建令牌服务
	tokenService := sysImpl.NewEmptyTokenService()

	// 注册路由 - 需要认证
	monitorGroup := r.Group("/monitor")
	monitorGroup.Use(middleware.Auth(tokenService))
	{
		// 定时任务管理
		jobGroup := monitorGroup.Group("/job")
		{
			jobGroup.GET("/list", middleware.Permission("monitor:job:list", nil), jobController.List)
			jobGroup.GET("/:jobId", middleware.Permission("monitor:job:query", nil), jobController.GetInfo)
			jobGroup.POST("", middleware.Permission("monitor:job:add", nil), jobController.Add)
			jobGroup.PUT("", middleware.Permission("monitor:job:edit", nil), jobController.Edit)
			jobGroup.DELETE("/:jobIds", middleware.Permission("monitor:job:remove", nil), jobController.Remove)
			jobGroup.PUT("/changeStatus", middleware.Permission("monitor:job:changeStatus", nil), jobController.ChangeStatus)
			jobGroup.PUT("/run", middleware.Permission("monitor:job:changeStatus", nil), jobController.Run)
		}

		// 定时任务日志管理
		jobLogGroup := monitorGroup.Group("/jobLog")
		{
			jobLogGroup.GET("/list", middleware.Permission("monitor:job:list", nil), jobLogController.List)
			jobLogGroup.GET("/:jobLogId", middleware.Permission("monitor:job:query", nil), jobLogController.GetInfo)
			jobLogGroup.DELETE("/:jobLogIds", middleware.Permission("monitor:job:remove", nil), jobLogController.Remove)
			jobLogGroup.DELETE("/clean", middleware.Permission("monitor:job:remove", nil), jobLogController.Clean)
		}
	}
}
