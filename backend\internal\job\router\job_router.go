package router

import (
	"backend/internal/api/middleware"
	"backend/internal/job/controller"
	"backend/internal/job/repository"
	"backend/internal/job/service"
	"backend/internal/service/impl"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// RegisterJobRouter 注册任务调度相关路由
func RegisterJobRouter(r *gin.RouterGroup, db *gorm.DB) {
	// 创建仓库
	jobRepo := repository.NewSysJobRepository(db)

	// 创建服务
	jobService := service.NewSysJobService(jobRepo)

	// 创建控制器
	jobController := controller.NewSysJobController(jobService)

	// 创建令牌服务
	tokenService := impl.NewEmptyTokenService()
	permissionService := &impl.EmptyPermissionService{}

	// 注册路由 - 需要认证
	monitorGroup := r.Group("/monitor")
	monitorGroup.Use(middleware.Auth(tokenService))
	{
		// 定时任务管理
		jobGroup := monitorGroup.Group("/job")
		{
			jobGroup.GET("/list", middleware.Permission("monitor:job:list", permissionService), jobController.List)
			jobGroup.GET("/:jobId", middleware.Permission("monitor:job:query", permissionService), jobController.GetInfo)
			jobGroup.POST("", middleware.Permission("monitor:job:add", permissionService), jobController.Add)
			jobGroup.PUT("", middleware.Permission("monitor:job:edit", permissionService), jobController.Edit)
			jobGroup.DELETE("/:jobIds", middleware.Permission("monitor:job:remove", permissionService), jobController.Remove)
			jobGroup.PUT("/changeStatus", middleware.Permission("monitor:job:changeStatus", permissionService), jobController.ChangeStatus)
			jobGroup.PUT("/run", middleware.Permission("monitor:job:changeStatus", permissionService), jobController.Run)
			jobGroup.GET("/checkCronExpressionIsValid", jobController.CheckCronExpressionIsValid)
		}
	}
}
