package system

import (
	"backend/internal/api/controller"
	"backend/internal/model"
	"backend/internal/service"

	"github.com/gin-gonic/gin"
)

// SysRegisterController 注册验证控制器
type SysRegisterController struct {
	controller.BaseController
	registerService service.SysRegisterService
	configService   service.SysConfigService
}

// NewSysRegisterController 创建注册控制器
func NewSysRegisterController(registerService service.SysRegisterService, configService service.SysConfigService) *SysRegisterController {
	return &SysRegisterController{
		registerService: registerService,
		configService:   configService,
	}
}

// Register 用户注册
// @Summary 用户注册
// @Description 用户注册接口
// @Tags 用户注册
// @Accept json
// @Produce json
// @Param user body model.RegisterBody true "注册信息"
// @Success 200 {object} controller.Response "成功"
// @Router /register [post]
func (c *SysRegisterController) Register(ctx *gin.Context) {
	// 检查是否开启注册功能
	registerEnabled, err := c.configService.SelectConfigByKey("sys.account.registerUser")
	if err != nil {
		c.Error(ctx, err)
		return
	}

	if registerEnabled != "true" {
		c.Warn(ctx, "当前系统没有开启注册功能！")
		return
	}

	// 解析注册信息
	var user model.RegisterBody
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.Error(ctx, err)
		return
	}

	// 调用注册服务
	msg, err := c.registerService.Register(&user)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	if msg == "" {
		c.Success(ctx, nil)
	} else {
		c.Warn(ctx, msg)
	}
}
