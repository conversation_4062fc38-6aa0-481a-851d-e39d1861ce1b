package util

import (
	"time"

	"github.com/robfig/cron/v3"
)

// IsValidCronExpression 校验Cron表达式是否有效
func IsValidCronExpression(cronExpression string) bool {
	_, err := cron.ParseStandard(cronExpression)
	return err == nil
}

// GetNextExecution 获取下一次执行时间
func GetNextExecution(cronExpression string) (*time.Time, error) {
	schedule, err := cron.ParseStandard(cronExpression)
	if err != nil {
		return nil, err
	}

	next := schedule.Next(time.Now())
	return &next, nil
}
