package server

import (
	"backend/internal/utils"
)

// Mem 内存相关信息
type Mem struct {
	// 内存总量
	Total int64 `json:"total"`

	// 已用内存
	Used int64 `json:"used"`

	// 剩余内存
	Free int64 `json:"free"`
}

// GetTotal 获取内存总量(GB)
func (m *Mem) GetTotal() float64 {
	return utils.Arith.Div(float64(m.Total), float64(1024*1024*1024), 2)
}

// GetUsed 获取已用内存(GB)
func (m *Mem) GetUsed() float64 {
	return utils.Arith.Div(float64(m.Used), float64(1024*1024*1024), 2)
}

// GetFree 获取剩余内存(GB)
func (m *Mem) GetFree() float64 {
	return utils.Arith.Div(float64(m.Free), float64(1024*1024*1024), 2)
}

// GetUsage 获取内存使用率
func (m *Mem) GetUsage() float64 {
	if m.Total == 0 {
		return 0
	}
	return utils.Arith.Mul(utils.Arith.Div(float64(m.Used), float64(m.Total), 4), 100)
}
