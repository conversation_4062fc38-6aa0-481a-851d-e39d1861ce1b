# Java后端到Go API接口映射表

## 🔐 认证相关接口

| Java Controller | 接口路径 | 方法 | Go Controller | 状态 |
|----------------|----------|------|---------------|------|
| SysLoginController.login | POST /login | 用户登录 | SysLoginController.Login | ✅ |
| SysLoginController.getInfo | GET /getInfo | 获取用户信息 | SysLoginController.GetInfo | ✅ |
| SysLoginController.getRouters | GET /getRouters | 获取路由信息 | SysLoginController.GetRouters | ✅ |
| SysLoginController.logout | POST /logout | 用户登出 | SysLoginController.Logout | ✅ |

## 👥 用户管理接口

| Java Controller | 接口路径 | 方法 | Go Controller | 状态 |
|----------------|----------|------|---------------|------|
| SysUserController.list | GET /system/user/list | 用户列表 | SysUserController.List | ✅ |
| SysUserController.getInfo | GET /system/user/{userId} | 用户详情 | SysUserController.GetInfo | ✅ |
| SysUserController.add | POST /system/user | 新增用户 | SysUserController.Add | ✅ |
| SysUserController.edit | PUT /system/user | 修改用户 | SysUserController.Edit | ✅ |
| SysUserController.remove | DELETE /system/user/{userIds} | 删除用户 | SysUserController.Remove | ✅ |
| SysUserController.resetPwd | PUT /system/user/resetPwd | 重置密码 | SysUserController.ResetPwd | ✅ |
| SysUserController.changeStatus | PUT /system/user/changeStatus | 状态修改 | SysUserController.ChangeStatus | ✅ |

## 🎭 角色管理接口

| Java Controller | 接口路径 | 方法 | Go Controller | 状态 |
|----------------|----------|------|---------------|------|
| SysRoleController.list | GET /system/role/list | 角色列表 | SysRoleController.List | ✅ |
| SysRoleController.getInfo | GET /system/role/{roleId} | 角色详情 | SysRoleController.GetInfo | ✅ |
| SysRoleController.add | POST /system/role | 新增角色 | SysRoleController.Add | ✅ |
| SysRoleController.edit | PUT /system/role | 修改角色 | SysRoleController.Edit | ✅ |
| SysRoleController.remove | DELETE /system/role/{roleIds} | 删除角色 | SysRoleController.Remove | ✅ |
| SysRoleController.changeStatus | PUT /system/role/changeStatus | 状态修改 | SysRoleController.ChangeStatus | ✅ |
| SysRoleController.optionselect | GET /system/role/optionselect | 角色选择框 | SysRoleController.OptionSelect | ✅ |

## 📋 菜单管理接口

| Java Controller | 接口路径 | 方法 | Go Controller | 状态 |
|----------------|----------|------|---------------|------|
| SysMenuController.list | GET /system/menu/list | 菜单列表 | SysMenuController.List | ✅ |
| SysMenuController.getInfo | GET /system/menu/{menuId} | 菜单详情 | SysMenuController.GetInfo | ✅ |
| SysMenuController.treeselect | GET /system/menu/treeselect | 菜单树选择 | SysMenuController.TreeSelect | ✅ |
| SysMenuController.add | POST /system/menu | 新增菜单 | SysMenuController.Add | ✅ |
| SysMenuController.edit | PUT /system/menu | 修改菜单 | SysMenuController.Edit | ✅ |
| SysMenuController.remove | DELETE /system/menu/{menuId} | 删除菜单 | SysMenuController.Remove | ✅ |

## 🏢 部门管理接口

| Java Controller | 接口路径 | 方法 | Go Controller | 状态 |
|----------------|----------|------|---------------|------|
| SysDeptController.list | GET /system/dept/list | 部门列表 | SysDeptController.List | ✅ |
| SysDeptController.getInfo | GET /system/dept/{deptId} | 部门详情 | SysDeptController.GetInfo | ✅ |
| SysDeptController.treeselect | GET /system/dept/treeselect | 部门树选择 | SysDeptController.TreeSelect | ✅ |
| SysDeptController.add | POST /system/dept | 新增部门 | SysDeptController.Add | ✅ |
| SysDeptController.edit | PUT /system/dept | 修改部门 | SysDeptController.Edit | ✅ |
| SysDeptController.remove | DELETE /system/dept/{deptId} | 删除部门 | SysDeptController.Remove | ✅ |

## 💼 岗位管理接口

| Java Controller | 接口路径 | 方法 | Go Controller | 状态 |
|----------------|----------|------|---------------|------|
| SysPostController.list | GET /system/post/list | 岗位列表 | SysPostController.List | ✅ |
| SysPostController.getInfo | GET /system/post/{postId} | 岗位详情 | SysPostController.GetInfo | ✅ |
| SysPostController.add | POST /system/post | 新增岗位 | SysPostController.Add | ✅ |
| SysPostController.edit | PUT /system/post | 修改岗位 | SysPostController.Edit | ✅ |
| SysPostController.remove | DELETE /system/post/{postIds} | 删除岗位 | SysPostController.Remove | ✅ |

## 📚 字典管理接口

| Java Controller | 接口路径 | 方法 | Go Controller | 状态 |
|----------------|----------|------|---------------|------|
| SysDictTypeController.list | GET /system/dict/type/list | 字典类型列表 | SysDictTypeController.List | ✅ |
| SysDictDataController.list | GET /system/dict/data/list | 字典数据列表 | SysDictDataController.List | ✅ |

## ⚙️ 参数配置接口

| Java Controller | 接口路径 | 方法 | Go Controller | 状态 |
|----------------|----------|------|---------------|------|
| SysConfigController.list | GET /system/config/list | 参数列表 | SysConfigController.List | ✅ |
| SysConfigController.getInfo | GET /system/config/{configId} | 参数详情 | SysConfigController.GetInfo | ✅ |

## 📢 通知公告接口

| Java Controller | 接口路径 | 方法 | Go Controller | 状态 |
|----------------|----------|------|---------------|------|
| SysNoticeController.list | GET /system/notice/list | 公告列表 | SysNoticeController.List | ✅ |
| SysNoticeController.getInfo | GET /system/notice/{noticeId} | 公告详情 | SysNoticeController.GetInfo | ✅ |

## 📊 监控相关接口

| Java Controller | 接口路径 | 方法 | Go Controller | 状态 |
|----------------|----------|------|---------------|------|
| SysLogininforController.list | GET /monitor/logininfor/list | 登录日志 | SysLogininforController.List | ✅ |
| SysOperlogController.list | GET /monitor/operlog/list | 操作日志 | SysOperlogController.List | ✅ |
| SysUserOnlineController.list | GET /monitor/online/list | 在线用户 | SysUserOnlineController.List | ✅ |
| ServerController.getInfo | GET /monitor/server | 服务器信息 | ServerController.GetInfo | ✅ |
| CacheController.getInfo | GET /monitor/cache | 缓存信息 | CacheController.GetInfo | ✅ |

## 🔧 通用接口

| Java Controller | 接口路径 | 方法 | Go Controller | 状态 |
|----------------|----------|------|---------------|------|
| CaptchaController.getCode | GET /captchaImage | 验证码 | CaptchaController.GetCode | ✅ |
| CommonController.uploadFile | POST /common/upload | 文件上传 | CommonController.UploadFile | ✅ |
| CommonController.download | GET /common/download | 文件下载 | CommonController.Download | ✅ |

## ⏰ 定时任务接口

| Java Controller | 接口路径 | 方法 | Go Controller | 状态 |
|----------------|----------|------|---------------|------|
| SysJobController.list | GET /monitor/job/list | 任务列表 | SysJobController.List | ✅ |
| SysJobLogController.list | GET /monitor/jobLog/list | 任务日志 | SysJobLogController.List | ✅ |

## 📝 接口规范说明

### 请求格式
- **Content-Type**: application/json
- **Authorization**: Bearer {token}

### 响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

### 分页格式
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [],
  "total": 100
}
```

### 错误码
- 200: 成功
- 401: 未授权
- 403: 禁止访问
- 500: 服务器错误
