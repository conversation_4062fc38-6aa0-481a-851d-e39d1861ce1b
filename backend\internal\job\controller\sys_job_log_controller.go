package controller

import (
	"backend/internal/job/service"
	"backend/internal/model"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysJobLogController 定时任务日志控制器
type SysJobLogController struct {
	jobLogService service.SysJobLogService
}

// NewSysJobLogController 创建定时任务日志控制器
func NewSysJobLogController(jobLogService service.SysJobLogService) *SysJobLogController {
	return &SysJobLogController{
		jobLogService: jobLogService,
	}
}

// List 查询定时任务调度日志列表
func (c *SysJobLogController) List(ctx *gin.Context) {
	// 构建查询参数
	jobLog := &model.SysJobLog{
		JobName:      ctx.Query("jobName"),
		JobGroup:     ctx.Query("jobGroup"),
		Status:       ctx.Query("status"),
		InvokeTarget: ctx.Query("invokeTarget"),
	}

	// 查询列表
	list, err := c.jobLogService.SelectJobLogList(jobLog)
	if err != nil {
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "查询定时任务日志列表失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code":  200,
		"msg":   "查询成功",
		"rows":  list,
		"total": len(list),
	})
}

// GetInfo 根据调度编号获取详细信息
func (c *SysJobLogController) GetInfo(ctx *gin.Context) {
	jobLogId, err := strconv.ParseInt(ctx.Param("jobLogId"), 10, 64)
	if err != nil {
		ctx.JSON(400, gin.H{
			"code": 400,
			"msg":  "无效的任务日志ID",
		})
		return
	}

	jobLog, err := c.jobLogService.SelectJobLogById(jobLogId)
	if err != nil {
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "获取定时任务日志失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "查询成功",
		"data": jobLog,
	})
}

// Remove 删除定时任务调度日志
func (c *SysJobLogController) Remove(ctx *gin.Context) {
	jobLogIdsStr := ctx.Param("jobLogIds")
	jobLogIdsArr := strings.Split(jobLogIdsStr, ",")
	jobLogIds := make([]int64, 0, len(jobLogIdsArr))

	for _, idStr := range jobLogIdsArr {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			ctx.JSON(400, gin.H{
				"code": 400,
				"msg":  "无效的任务日志ID: " + idStr,
			})
			return
		}
		jobLogIds = append(jobLogIds, id)
	}

	rows, err := c.jobLogService.DeleteJobLogByIds(jobLogIds)
	if err != nil {
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "删除任务日志失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "删除成功",
		"data": rows,
	})
}

// Clean 清空定时任务调度日志
func (c *SysJobLogController) Clean(ctx *gin.Context) {
	err := c.jobLogService.CleanJobLog()
	if err != nil {
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "清空任务日志失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "清空成功",
	})
}
