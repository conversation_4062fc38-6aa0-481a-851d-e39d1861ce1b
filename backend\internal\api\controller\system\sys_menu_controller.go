package system

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"backend/internal/api/controller"
	"backend/internal/constants"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
)

// SysMenuController 菜单信息控制器
type SysMenuController struct {
	controller.BaseController
	menuService service.SysMenuService
}

// NewSysMenuController 创建菜单控制器实例
func NewSysMenuController(menuService service.SysMenuService) *SysMenuController {
	return &SysMenuController{
		menuService: menuService,
	}
}

// List 获取菜单列表
// @Summary 获取菜单列表
// @Description 获取菜单列表
// @Tags 菜单管理
// @Accept  json
// @Produce json
// @Param menu query model.SysMenu false "查询参数"
// @Success 200 {object} controller.Response "成功"
// @Router /system/menu/list [get]
func (c *SysMenuController) List(ctx *gin.Context) {
	// 获取当前用户ID
	userId := c.GetUserId(ctx)

	// 创建查询对象
	menu := &model.SysMenu{
		MenuName: ctx.Query("menuName"),
		Status:   ctx.Query("status"),
	}

	// 调用服务查询菜单列表
	menus, err := c.menuService.SelectMenuListWithCondition(menu, userId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, menus)
}

// GetInfo 根据菜单编号获取详细信息
// @Summary 根据菜单编号获取详细信息
// @Description 根据菜单编号获取详细信息
// @Tags 菜单管理
// @Accept  json
// @Produce json
// @Param menuId path int true "菜单ID"
// @Success 200 {object} controller.Response "成功"
// @Router /system/menu/{menuId} [get]
func (c *SysMenuController) GetInfo(ctx *gin.Context) {
	menuId, err := strconv.ParseInt(ctx.Param("menuId"), 10, 64)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	menu, err := c.menuService.SelectMenuById(menuId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, menu)
}

// TreeSelect 获取菜单下拉树列表
// @Summary 获取菜单下拉树列表
// @Description 获取菜单下拉树列表
// @Tags 菜单管理
// @Accept  json
// @Produce json
// @Param menu query model.SysMenu false "查询参数"
// @Success 200 {object} controller.Response "成功"
// @Router /system/menu/treeselect [get]
func (c *SysMenuController) TreeSelect(ctx *gin.Context) {
	// 获取当前用户ID
	userId := c.GetUserId(ctx)

	// 创建查询对象
	menu := &model.SysMenu{
		MenuName: ctx.Query("menuName"),
		Status:   ctx.Query("status"),
	}

	// 调用服务查询菜单列表
	menus, err := c.menuService.SelectMenuListWithCondition(menu, userId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 构建树结构
	treeSelect, err := c.menuService.BuildMenuTreeSelect(menus)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, treeSelect)
}

// RoleMenuTreeSelect 加载对应角色菜单列表树
// @Summary 加载对应角色菜单列表树
// @Description 加载对应角色菜单列表树
// @Tags 菜单管理
// @Accept  json
// @Produce json
// @Param roleId path int true "角色ID"
// @Success 200 {object} controller.Response "成功"
// @Router /system/menu/roleMenuTreeselect/{roleId} [get]
func (c *SysMenuController) RoleMenuTreeSelect(ctx *gin.Context) {
	roleId, err := strconv.ParseInt(ctx.Param("roleId"), 10, 64)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 获取当前用户ID
	userId := c.GetUserId(ctx)

	// 查询菜单列表
	menus, err := c.menuService.SelectMenuList(userId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 查询角色已有菜单列表
	checkedKeys, err := c.menuService.SelectMenuListByRoleId(roleId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 构建树结构
	treeSelect, err := c.menuService.BuildMenuTreeSelect(menus)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	result := gin.H{
		"checkedKeys": checkedKeys,
		"menus":       treeSelect,
	}

	c.Success(ctx, result)
}

// Add 新增菜单
// @Summary 新增菜单
// @Description 新增菜单
// @Tags 菜单管理
// @Accept  json
// @Produce json
// @Param menu body model.SysMenu true "菜单信息"
// @Success 200 {object} controller.Response "成功"
// @Router /system/menu [post]
func (c *SysMenuController) Add(ctx *gin.Context) {
	var menu model.SysMenu
	if err := ctx.ShouldBindJSON(&menu); err != nil {
		c.Error(ctx, err)
		return
	}

	// 检查菜单名称是否唯一
	unique, err := c.menuService.CheckMenuNameUnique(&menu)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	if !unique {
		c.Error(ctx, utils.NewError("新增菜单'"+menu.MenuName+"'失败，菜单名称已存在"))
		return
	}

	// 检查外链
	if constants.YES_FRAME == menu.IsFrame && !utils.IsHttp(menu.Path) {
		c.Error(ctx, utils.NewError("新增菜单'"+menu.MenuName+"'失败，地址必须以http(s)://开头"))
		return
	}

	// 设置创建者
	menu.CreateBy = c.GetUsername(ctx)

	// 插入菜单
	_, err = c.menuService.InsertMenu(&menu)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// Edit 修改菜单
// @Summary 修改菜单
// @Description 修改菜单
// @Tags 菜单管理
// @Accept  json
// @Produce json
// @Param menu body model.SysMenu true "菜单信息"
// @Success 200 {object} controller.Response "成功"
// @Router /system/menu [put]
func (c *SysMenuController) Edit(ctx *gin.Context) {
	var menu model.SysMenu
	if err := ctx.ShouldBindJSON(&menu); err != nil {
		c.Error(ctx, err)
		return
	}

	// 检查菜单名称是否唯一
	unique, err := c.menuService.CheckMenuNameUnique(&menu)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	if !unique {
		c.Error(ctx, utils.NewError("修改菜单'"+menu.MenuName+"'失败，菜单名称已存在"))
		return
	}

	// 检查外链
	if constants.YES_FRAME == menu.IsFrame && !utils.IsHttp(menu.Path) {
		c.Error(ctx, utils.NewError("修改菜单'"+menu.MenuName+"'失败，地址必须以http(s)://开头"))
		return
	}

	// 检查父菜单不能选择自己
	if menu.MenuID == menu.ParentID {
		c.Error(ctx, utils.NewError("修改菜单'"+menu.MenuName+"'失败，上级菜单不能选择自己"))
		return
	}

	// 设置更新者
	menu.UpdateBy = c.GetUsername(ctx)

	// 更新菜单
	_, err = c.menuService.UpdateMenu(&menu)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// Remove 删除菜单
// @Summary 删除菜单
// @Description 删除菜单
// @Tags 菜单管理
// @Accept  json
// @Produce json
// @Param menuId path int true "菜单ID"
// @Success 200 {object} controller.Response "成功"
// @Router /system/menu/{menuId} [delete]
func (c *SysMenuController) Remove(ctx *gin.Context) {
	menuId, err := strconv.ParseInt(ctx.Param("menuId"), 10, 64)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 判断是否存在子菜单
	hasChild, err := c.menuService.HasChildByMenuId(menuId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	if hasChild {
		c.Warn(ctx, "存在子菜单,不允许删除")
		return
	}

	// 判断菜单是否已分配
	hasRole, err := c.menuService.CheckMenuExistRole(menuId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	if hasRole {
		c.Warn(ctx, "菜单已分配,不允许删除")
		return
	}

	// 删除菜单
	_, err = c.menuService.DeleteMenuById(menuId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}
