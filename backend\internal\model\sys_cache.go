package model

// SysCache 系统缓存信息
type SysCache struct {
	// 缓存名称
	CacheName string `json:"cacheName"`

	// 缓存键名
	CacheKey string `json:"cacheKey"`

	// 缓存内容
	CacheValue string `json:"cacheValue"`

	// 备注
	Remark string `json:"remark"`
}

// NewSysCache 创建缓存信息
func NewSysCache(cacheName, remark string) *SysCache {
	return &SysCache{
		CacheName: cacheName,
		Remark:    remark,
	}
}

// NewSysCacheWithKeyValue 创建带键值的缓存信息
func NewSysCacheWithKeyValue(cacheName, cacheKey, cacheValue string) *SysCache {
	return &SysCache{
		CacheName:  cacheName,
		CacheKey:   cacheKey,
		CacheValue: cacheValue,
	}
}
