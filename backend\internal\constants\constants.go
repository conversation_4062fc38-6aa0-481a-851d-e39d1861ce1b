package constants

// 系统内置（是否系统内置）
const (
	// SYS_YES 是
	SYS_YES = "Y"
	// SYS_NO 否
	SYS_NO = "N"
)

// 用户状态
const (
	// USER_STATUS_NORMAL 正常
	USER_STATUS_NORMAL = "0"
	// USER_STATUS_DISABLE 停用
	USER_STATUS_DISABLE = "1"
	// USER_STATUS_DELETED 删除
	USER_STATUS_DELETED = "2"
)

// 校验返回结果码
const (
	// VALIDATE_UNIQUE 唯一
	VALIDATE_UNIQUE = true
	// VALIDATE_NOT_UNIQUE 不唯一
	VALIDATE_NOT_UNIQUE = false
)

// 用户名长度限制
const (
	// USERNAME_MIN_LENGTH 用户名最小长度
	USERNAME_MIN_LENGTH = 2
	// USERNAME_MAX_LENGTH 用户名最大长度
	USERNAME_MAX_LENGTH = 20
)

// 密码长度限制
const (
	// PASSWORD_MIN_LENGTH 密码最小长度
	PASSWORD_MIN_LENGTH = 5
	// PASSWORD_MAX_LENGTH 密码最大长度
	PASSWORD_MAX_LENGTH = 20
)

// 手机号码格式限制
const (
	// MOBILE_PHONE_NUMBER_PATTERN 手机号码格式
	MOBILE_PHONE_NUMBER_PATTERN = "^1[3|4|5|6|7|8|9][0-9]\\d{8}$"
)

// 邮箱格式限制
const (
	// EMAIL_PATTERN 邮箱格式
	EMAIL_PATTERN = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$"
)

// 菜单类型
const (
	// MENU_TYPE_DIR 目录
	MENU_TYPE_DIR = "M"
	// MENU_TYPE_MENU 菜单
	MENU_TYPE_MENU = "C"
	// MENU_TYPE_BUTTON 按钮
	MENU_TYPE_BUTTON = "F"
)

// 是否菜单外链
const (
	// MENU_YES_FRAME 是
	MENU_YES_FRAME = "0"
	// MENU_NO_FRAME 否
	MENU_NO_FRAME = "1"
)

// 菜单状态
const (
	// MENU_VISIBLE 显示
	MENU_VISIBLE = "0"
	// MENU_HIDE 隐藏
	MENU_HIDE = "1"
)

// 是否为系统默认（是否默认）
const (
	// SYS_DEFAULT 是
	SYS_DEFAULT = "Y"
	// SYS_NOT_DEFAULT 否
	SYS_NOT_DEFAULT = "N"
)

// 数据范围
const (
	// ROLE_DATA_SCOPE_ALL 所有数据权限
	ROLE_DATA_SCOPE_ALL = "1"
	// ROLE_DATA_SCOPE_CUSTOM 自定义数据权限
	ROLE_DATA_SCOPE_CUSTOM = "2"
	// ROLE_DATA_SCOPE_DEPT 部门数据权限
	ROLE_DATA_SCOPE_DEPT = "3"
	// ROLE_DATA_SCOPE_DEPT_AND_CHILD 部门及以下数据权限
	ROLE_DATA_SCOPE_DEPT_AND_CHILD = "4"
	// ROLE_DATA_SCOPE_SELF 仅本人数据权限
	ROLE_DATA_SCOPE_SELF = "5"
)

// 用户常量
const (
	// 平台内系统用户的唯一标志
	SYS_USER = "SYS_USER"
	// 正常状态
	NORMAL = "0"
	// 异常状态
	EXCEPTION = "1"
	// 用户封禁状态
	USER_DISABLE = "1"
	// 角色封禁状态
	ROLE_DISABLE = "1"
	// 部门正常状态
	DEPT_NORMAL = "0"
	// 部门停用状态
	DEPT_DISABLE = "1"
	// 字典正常状态
	DICT_NORMAL = "0"
	// 是否为系统默认（是）
	YES = "Y"
	// 是否菜单外链（是）
	YES_FRAME = "0"
	// 是否菜单外链（否）
	NO_FRAME = "1"
	// 菜单类型（目录）
	TYPE_DIR = "M"
	// 菜单类型（菜单）
	TYPE_MENU = "C"
	// 菜单类型（按钮）
	TYPE_BUTTON = "F"
	// Layout组件标识
	LAYOUT = "Layout"
	// ParentView组件标识
	PARENT_VIEW = "ParentView"
	// InnerLink组件标识
	INNER_LINK = "InnerLink"
	// 校验返回结果码
	SUCCESS = "0"
	// 校验返回结果码
	FAIL = "1"
	// 登录成功状态
	LOGIN_SUCCESS = "0"
	// 登录失败状态
	LOGIN_FAIL = "1"
	// 验证码有效期（分钟）
	CAPTCHA_EXPIRATION = 2
	// 令牌
	TOKEN = "token"
	// 令牌前缀
	TOKEN_PREFIX = "Bearer "
	// 令牌前缀
	LOGIN_USER_KEY = "login_user_key"
	// 用户ID
	JWT_USERID = "userid"
	// 用户名称
	JWT_USERNAME = "sub"
	// 用户头像
	JWT_AVATAR = "avatar"
	// 创建时间
	JWT_CREATED = "created"
	// 用户权限
	JWT_AUTHORITIES = "authorities"
	// 参数管理 cache key（已在cache_constants.go中定义）
	// SYS_CONFIG_KEY = "sys_config:"
	// 字典管理 cache key（已在cache_constants.go中定义）
	// SYS_DICT_KEY = "sys_dict:"
	// 资源映射路径 前缀
	RESOURCE_PREFIX = "/profile"
	// RMI 远程方法调用
	LOOKUP_RMI = "rmi:"
	// LDAP 远程方法调用
	LOOKUP_LDAP = "ldap:"
	// LDAPS 远程方法调用
	LOOKUP_LDAPS = "ldaps:"
	// 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
	JOB_WHITELIST_STR = "com.ruoyi"
	// 定时任务违规的字符
	JOB_ERROR_STR = "Document,CommandProcess,Runtime,URL,SpringHttpInvoker,JNDIExporter,SpringHttpInvoker,XMLDecoder,ClassLocator,Locale"
)

// 角色常量
const (
	// 超级管理员ID
	ADMIN = 1
	// 普通角色
	COMMON_ROLE = 2
	// 超级管理员角色
	ADMIN_ROLE_KEY = "admin"
	// 默认管理员密码
	DEFAULT_PASSWORD = "123456"
)

// 数据范围类型
const (
	// 全部数据权限
	DATA_SCOPE_ALL = "1"
	// 自定数据权限
	DATA_SCOPE_CUSTOM = "2"
	// 部门数据权限
	DATA_SCOPE_DEPT = "3"
	// 部门及以下数据权限
	DATA_SCOPE_DEPT_AND_CHILD = "4"
	// 仅本人数据权限
	DATA_SCOPE_SELF = "5"
	// 数据权限
	DATA_SCOPE = "dataScope"
)

// 认证相关常量
const (
	// 认证头
	AUTHENTICATION = "Authorization"
	// 用户ID键
	USER_ID_KEY = "userId"
	// 用户名键
	USERNAME_KEY = "username"
	// URL参数中的TOKEN参数
	TOKEN_PARAM = "token"
)
