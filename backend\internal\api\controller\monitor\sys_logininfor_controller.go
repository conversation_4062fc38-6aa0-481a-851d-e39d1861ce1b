package monitor

import (
	"backend/internal/api/controller"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysLogininforController 系统访问记录
type SysLogininforController struct {
	controller.BaseController
	logininforService service.SysLogininforService
	passwordService   service.SysPasswordService
}

// NewSysLogininforController 创建登录日志控制器
func NewSysLogininforController(logininforService service.SysLogininforService, passwordService service.SysPasswordService) *SysLogininforController {
	return &SysLogininforController{
		logininforService: logininforService,
		passwordService:   passwordService,
	}
}

// List 查询系统访问记录列表
// @Summary 查询系统访问记录列表
// @Description 查询系统访问记录列表
// @Tags 登录日志
// @Accept json
// @Produce json
// @Param logininfor query model.SysLogininfor false "查询参数"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/logininfor/list [get]
func (c *SysLogininforController) List(ctx *gin.Context) {
	// 构建查询参数
	logininfor := &model.SysLogininfor{
		UserName: ctx.Query("userName"),
		Ipaddr:   ctx.Query("ipaddr"),
		Status:   ctx.Query("status"),
	}

	// 查询登录日志列表
	list, err := c.logininforService.SelectLogininforList(logininfor)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, list)
}

// Export 导出登录日志
// @Summary 导出登录日志
// @Description 导出登录日志
// @Tags 登录日志
// @Accept json
// @Produce octet-stream
// @Param logininfor query model.SysLogininfor false "查询参数"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/logininfor/export [post]
func (c *SysLogininforController) Export(ctx *gin.Context) {
	// 导出功能需要实现Excel导出，暂时返回提示信息
	c.Warn(ctx, "导出功能暂未实现")
}

// Remove 删除登录日志
// @Summary 删除登录日志
// @Description 删除登录日志
// @Tags 登录日志
// @Accept json
// @Produce json
// @Param infoIds path string true "登录日志ID，多个以逗号分隔"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/logininfor/{infoIds} [delete]
func (c *SysLogininforController) Remove(ctx *gin.Context) {
	infoIdsStr := ctx.Param("infoIds")
	infoIdStrs := strings.Split(infoIdsStr, ",")

	var infoIds []int64
	for _, idStr := range infoIdStrs {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			c.Error(ctx, utils.NewError("参数错误"))
			return
		}
		infoIds = append(infoIds, id)
	}

	rows, err := c.logininforService.DeleteLogininforByIds(infoIds)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, rows)
}

// Clean 清空登录日志
// @Summary 清空登录日志
// @Description 清空登录日志
// @Tags 登录日志
// @Accept json
// @Produce json
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/logininfor/clean [delete]
func (c *SysLogininforController) Clean(ctx *gin.Context) {
	err := c.logininforService.CleanLogininfor()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// Unlock 解锁账户
// @Summary 解锁账户
// @Description 解锁账户
// @Tags 登录日志
// @Accept json
// @Produce json
// @Param userName path string true "用户名"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/logininfor/unlock/{userName} [get]
func (c *SysLogininforController) Unlock(ctx *gin.Context) {
	userName := ctx.Param("userName")
	err := c.passwordService.ClearLoginRecordCache(userName)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}
