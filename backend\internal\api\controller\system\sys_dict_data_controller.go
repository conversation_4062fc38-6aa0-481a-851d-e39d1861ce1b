package system

import (
	"backend/internal/api/controller"
	"backend/internal/model"
	"backend/internal/service"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysDictDataController 字典数据控制器
type SysDictDataController struct {
	controller.BaseController
	dictDataService service.SysDictDataService
	dictTypeService service.SysDictTypeService
}

// NewSysDictDataController 创建字典数据控制器
func NewSysDictDataController(dictDataService service.SysDictDataService, dictTypeService service.SysDictTypeService) *SysDictDataController {
	return &SysDictDataController{
		dictDataService: dictDataService,
		dictTypeService: dictTypeService,
	}
}

// List 获取字典数据列表
// @Router /system/dict/data/list [get]
func (c *SysDictDataController) List(ctx *gin.Context) {
	var dictData model.SysDictData

	// 获取查询参数
	dictLabel := ctx.Query("dictLabel")
	dictData.DictLabel = dictLabel

	dictType := ctx.Query("dictType")
	dictData.DictType = dictType

	status := ctx.Query("status")
	dictData.Status = status

	// 获取分页参数
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	// 查询字典数据列表
	list, err := c.dictDataService.SelectDictDataList(&dictData)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// TODO: 实现分页
	// 返回数据
	c.Success(ctx, map[string]interface{}{
		"rows":     list,
		"total":    len(list),
		"pageNum":  pageNum,
		"pageSize": pageSize,
	})
}

// GetInfo 查询字典数据详细
// @Router /system/dict/data/{dictCode} [get]
func (c *SysDictDataController) GetInfo(ctx *gin.Context) {
	dictCodeStr := ctx.Param("dictCode")
	dictCode, err := strconv.ParseInt(dictCodeStr, 10, 64)
	if err != nil {
		c.Warn(ctx, "参数错误")
		return
	}

	dictData, err := c.dictDataService.SelectDictDataById(dictCode)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, dictData)
}

// DictType 根据字典类型查询字典数据信息
// @Router /system/dict/data/type/{dictType} [get]
func (c *SysDictDataController) DictType(ctx *gin.Context) {
	dictType := ctx.Param("dictType")

	dictDatas, err := c.dictTypeService.SelectDictDataByType(dictType)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	if dictDatas == nil {
		dictDatas = []*model.SysDictData{}
	}

	c.Success(ctx, dictDatas)
}

// Add 新增字典数据
// @Router /system/dict/data [post]
func (c *SysDictDataController) Add(ctx *gin.Context) {
	var dict model.SysDictData
	if err := ctx.ShouldBindJSON(&dict); err != nil {
		c.Error(ctx, err)
		return
	}

	// 设置创建者
	dict.CreateBy = c.GetUsername(ctx)

	dictCode, err := c.dictDataService.InsertDictData(&dict)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, dictCode, nil)
}

// Edit 修改保存字典数据
// @Router /system/dict/data [put]
func (c *SysDictDataController) Edit(ctx *gin.Context) {
	var dict model.SysDictData
	if err := ctx.ShouldBindJSON(&dict); err != nil {
		c.Error(ctx, err)
		return
	}

	// 设置更新者
	dict.UpdateBy = c.GetUsername(ctx)

	rows, err := c.dictDataService.UpdateDictData(&dict)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, rows, nil)
}

// Remove 删除字典数据
// @Router /system/dict/data/{dictCodes} [delete]
func (c *SysDictDataController) Remove(ctx *gin.Context) {
	dictCodesStr := ctx.Param("dictCodes")
	dictCodes := make([]int64, 0)

	for _, codeStr := range strings.Split(dictCodesStr, ",") {
		if code, err := strconv.ParseInt(codeStr, 10, 64); err == nil {
			dictCodes = append(dictCodes, code)
		}
	}

	err := c.dictDataService.DeleteDictDataByIds(dictCodes)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// RegisterRoutes 注册路由
func (c *SysDictDataController) RegisterRoutes(router *gin.RouterGroup) {
	dictDataRouter := router.Group("/system/dict/data")
	{
		dictDataRouter.GET("/list", c.List)
		dictDataRouter.GET("/:dictCode", c.GetInfo)
		dictDataRouter.GET("/type/:dictType", c.DictType)
		dictDataRouter.POST("", c.Add)
		dictDataRouter.PUT("", c.Edit)
		dictDataRouter.DELETE("/:dictCodes", c.Remove)
	}
}
