package impl

import (
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
)

// SysDictDataServiceImpl 字典数据服务实现
type SysDictDataServiceImpl struct {
	dictDataRepo repository.SysDictDataRepository
}

// NewSysDictDataService 创建字典数据服务
func NewSysDictDataService(dictDataRepo repository.SysDictDataRepository) service.SysDictDataService {
	return &SysDictDataServiceImpl{
		dictDataRepo: dictDataRepo,
	}
}

// SelectDictDataList 查询字典数据列表
func (s *SysDictDataServiceImpl) SelectDictDataList(dictData *model.SysDictData) ([]*model.SysDictData, error) {
	return s.dictDataRepo.SelectDictDataList(dictData)
}

// SelectDictDataById 根据字典数据ID查询信息
func (s *SysDictDataServiceImpl) SelectDictDataById(dictCode int64) (*model.SysDictData, error) {
	return s.dictDataRepo.SelectDictDataById(dictCode)
}

// SelectDictLabel 根据字典类型和字典值获取字典标签
func (s *SysDictDataServiceImpl) SelectDictLabel(dictType, dictValue string) (string, error) {
	return s.dictDataRepo.SelectDictLabel(dictType, dictValue)
}

// SelectDictDataByType 根据字典类型查询字典数据
func (s *SysDictDataServiceImpl) SelectDictDataByType(dictType string) ([]*model.SysDictData, error) {
	return s.dictDataRepo.SelectDictDataByType(dictType)
}

// DeleteDictDataById 删除字典数据
func (s *SysDictDataServiceImpl) DeleteDictDataById(dictCode int64) error {
	return s.dictDataRepo.DeleteDictDataById(dictCode)
}

// DeleteDictDataByIds 批量删除字典数据
func (s *SysDictDataServiceImpl) DeleteDictDataByIds(dictCodes []int64) error {
	// 逐个删除，更新缓存
	for _, dictCode := range dictCodes {
		dictData, err := s.SelectDictDataById(dictCode)
		if err == nil && dictData != nil {
			// 从数据库删除
			err = s.dictDataRepo.DeleteDictDataById(dictCode)
			if err != nil {
				return err
			}

			// 删除缓存
			dictCache.Delete(dictData.DictType)
		}
	}

	return nil
}

// InsertDictData 新增字典数据
func (s *SysDictDataServiceImpl) InsertDictData(dictData *model.SysDictData) (int64, error) {
	dictCode, err := s.dictDataRepo.InsertDictData(dictData)
	if err != nil {
		return 0, err
	}

	// 清除缓存
	dictCache.Delete(dictData.DictType)

	return dictCode, nil
}

// UpdateDictData 修改字典数据
func (s *SysDictDataServiceImpl) UpdateDictData(dictData *model.SysDictData) (int64, error) {
	// 查询旧数据
	oldDict, err := s.dictDataRepo.SelectDictDataById(dictData.DictCode)
	if err != nil {
		return 0, err
	}

	// 更新数据
	rows, err := s.dictDataRepo.UpdateDictData(dictData)
	if err != nil {
		return 0, err
	}

	// 清除缓存
	dictCache.Delete(oldDict.DictType)
	if oldDict.DictType != dictData.DictType {
		dictCache.Delete(dictData.DictType)
	}

	return rows, nil
}
