package system

import (
	"backend/internal/api/common"
	"backend/internal/model"
	"backend/internal/service"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysPostController 岗位信息控制器
type SysPostController struct {
	common.BaseController
	postService service.SysPostService
}

// NewSysPostController 创建岗位控制器
func NewSysPostController(postService service.SysPostService) *SysPostController {
	return &SysPostController{
		postService: postService,
	}
}

// List 获取岗位列表
// @Router /system/post/list [get]
func (c *SysPostController) List(ctx *gin.Context) {
	// 绑定查询参数
	post := &model.SysPost{}
	if postCode := ctx.Query("postCode"); postCode != "" {
		post.PostCode = postCode
	}
	if postName := ctx.Query("postName"); postName != "" {
		post.PostName = postName
	}
	if status := ctx.Query("status"); status != "" {
		post.Status = status
	}

	// 设置分页
	_ = c.StartPage(ctx) // 忽略返回值，暂未实现分页

	// 查询岗位列表
	posts, err := c.postService.SelectPostList(post)
	if err != nil {
		c.ErrorJSON(ctx, "查询岗位列表失败: "+err.Error())
		return
	}

	// TODO: 实现分页查询
	total := int64(len(posts))

	// 返回分页数据
	tableData := c.GetDataTable(posts, total)
	c.SuccessJSON(ctx, tableData)
}

// GetInfo 根据岗位编号获取详细信息
// @Router /system/post/{postId} [get]
func (c *SysPostController) GetInfo(ctx *gin.Context) {
	postIdStr := ctx.Param("postId")
	postId, err := strconv.ParseInt(postIdStr, 10, 64)
	if err != nil {
		c.ErrorJSON(ctx, "参数错误")
		return
	}

	post, err := c.postService.SelectPostById(postId)
	if err != nil {
		c.ErrorJSON(ctx, "查询岗位信息失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, post)
}

// Add 新增岗位
// @Router /system/post [post]
func (c *SysPostController) Add(ctx *gin.Context) {
	var post model.SysPost
	if err := ctx.ShouldBindJSON(&post); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验岗位名称是否唯一
	if !c.postService.CheckPostNameUnique(&post) {
		c.ErrorJSON(ctx, "新增岗位'"+post.PostName+"'失败，岗位名称已存在")
		return
	}

	// 校验岗位编码是否唯一
	if !c.postService.CheckPostCodeUnique(&post) {
		c.ErrorJSON(ctx, "新增岗位'"+post.PostName+"'失败，岗位编码已存在")
		return
	}

	// 设置创建者
	post.CreateBy = c.GetUsername(ctx)

	// 新增岗位
	result := c.postService.InsertPost(&post)

	c.JSON(ctx, common.Success, c.ToAjax(result))
}

// Edit 修改岗位
// @Router /system/post [put]
func (c *SysPostController) Edit(ctx *gin.Context) {
	var post model.SysPost
	if err := ctx.ShouldBindJSON(&post); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验岗位名称是否唯一
	if !c.postService.CheckPostNameUnique(&post) {
		c.ErrorJSON(ctx, "修改岗位'"+post.PostName+"'失败，岗位名称已存在")
		return
	}

	// 校验岗位编码是否唯一
	if !c.postService.CheckPostCodeUnique(&post) {
		c.ErrorJSON(ctx, "修改岗位'"+post.PostName+"'失败，岗位编码已存在")
		return
	}

	// 设置更新者
	post.UpdateBy = c.GetUsername(ctx)

	// 修改岗位
	result := c.postService.UpdatePost(&post)

	c.JSON(ctx, common.Success, c.ToAjax(result))
}

// Remove 删除岗位
// @Router /system/post/{postIds} [delete]
func (c *SysPostController) Remove(ctx *gin.Context) {
	postIdsStr := ctx.Param("postIds")
	postIdsArr := strings.Split(postIdsStr, ",")
	postIds := make([]int64, 0, len(postIdsArr))

	// 转换岗位ID
	for _, idStr := range postIdsArr {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		postIds = append(postIds, id)
	}

	// 删除岗位
	result := c.postService.DeletePostByIds(postIds)

	c.JSON(ctx, common.Success, c.ToAjax(result))
}

// OptionSelect 获取岗位选择框列表
// @Router /system/post/optionselect [get]
func (c *SysPostController) OptionSelect(ctx *gin.Context) {
	// 查询所有岗位
	posts, err := c.postService.SelectPostAll()
	if err != nil {
		c.ErrorJSON(ctx, "查询岗位列表失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, posts)
}
