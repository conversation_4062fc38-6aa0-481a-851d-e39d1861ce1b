#!/bin/bash

# 若依Go后端部署脚本
# 支持开发环境和生产环境部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
APP_NAME="ruoyi-go"
VERSION="1.0.0"
BUILD_DIR="build"
BACKEND_DIR="backend"
DOCKER_IMAGE="ruoyi-go:${VERSION}"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo "========================================"
    print_message $BLUE "$1"
    echo "========================================"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_message $RED "❌ $1 未安装或未配置到PATH"
        exit 1
    fi
}

# 检查环境
check_environment() {
    print_title "检查部署环境"
    
    print_message $YELLOW "🔍 检查Go环境..."
    check_command go
    print_message $GREEN "✅ Go环境检查通过"
    
    print_message $YELLOW "🔍 检查Docker环境..."
    check_command docker
    print_message $GREEN "✅ Docker环境检查通过"
    
    print_message $YELLOW "🔍 检查Docker Compose..."
    check_command docker-compose
    print_message $GREEN "✅ Docker Compose检查通过"
    
    print_message $YELLOW "🔍 检查项目结构..."
    if [ ! -d "$BACKEND_DIR" ]; then
        print_message $RED "❌ 后端目录 $BACKEND_DIR 不存在"
        exit 1
    fi
    
    if [ ! -f "$BACKEND_DIR/go.mod" ]; then
        print_message $RED "❌ go.mod文件不存在"
        exit 1
    fi
    
    print_message $GREEN "✅ 项目结构检查通过"
}

# 构建Go应用
build_go_app() {
    print_title "构建Go应用"
    
    cd $BACKEND_DIR
    
    print_message $YELLOW "📥 下载Go依赖..."
    go mod tidy
    
    print_message $YELLOW "🧪 运行测试..."
    go test ./... -v || print_message $YELLOW "⚠️  测试失败，但继续构建"
    
    print_message $YELLOW "🔨 编译Go应用..."
    mkdir -p $BUILD_DIR
    
    CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build \
        -ldflags "-w -s -X main.version=$VERSION -X main.buildTime=$(date)" \
        -o $BUILD_DIR/$APP_NAME .
    
    print_message $GREEN "✅ Go应用构建完成"
    
    cd ..
}

# 构建Docker镜像
build_docker_image() {
    print_title "构建Docker镜像"
    
    print_message $YELLOW "🐳 构建Docker镜像..."
    docker build -t $DOCKER_IMAGE .
    
    print_message $GREEN "✅ Docker镜像构建完成"
}

# 部署到开发环境
deploy_dev() {
    print_title "部署到开发环境"
    
    print_message $YELLOW "🚀 启动开发环境..."
    
    # 停止现有容器
    docker-compose down || true
    
    # 启动服务
    docker-compose up -d
    
    print_message $YELLOW "⏳ 等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        print_message $GREEN "✅ 开发环境部署成功"
        print_message $BLUE "📍 后端服务: http://localhost:8080"
        print_message $BLUE "📍 数据库: localhost:1433"
        print_message $BLUE "📍 Redis: localhost:6379"
    else
        print_message $RED "❌ 开发环境部署失败"
        docker-compose logs
        exit 1
    fi
}

# 部署到生产环境
deploy_prod() {
    print_title "部署到生产环境"
    
    print_message $YELLOW "🚀 启动生产环境..."
    
    # 使用生产配置
    export COMPOSE_FILE=docker-compose.yml:docker-compose.prod.yml
    
    # 停止现有容器
    docker-compose down || true
    
    # 启动服务
    docker-compose up -d
    
    print_message $YELLOW "⏳ 等待服务启动..."
    sleep 15
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        print_message $GREEN "✅ 生产环境部署成功"
        print_message $BLUE "📍 服务地址: http://localhost"
    else
        print_message $RED "❌ 生产环境部署失败"
        docker-compose logs
        exit 1
    fi
}

# 清理资源
cleanup() {
    print_title "清理部署资源"
    
    print_message $YELLOW "🧹 清理Docker资源..."
    
    # 停止容器
    docker-compose down
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的卷
    docker volume prune -f
    
    print_message $GREEN "✅ 资源清理完成"
}

# 查看日志
show_logs() {
    print_title "查看应用日志"
    
    print_message $YELLOW "📋 显示应用日志..."
    docker-compose logs -f ruoyi-backend
}

# 查看状态
show_status() {
    print_title "查看服务状态"
    
    print_message $YELLOW "📊 服务状态:"
    docker-compose ps
    
    print_message $YELLOW "📊 资源使用:"
    docker stats --no-stream
}

# 备份数据
backup_data() {
    print_title "备份数据"
    
    local backup_dir="backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $backup_dir
    
    print_message $YELLOW "💾 备份数据库..."
    docker-compose exec sqlserver /opt/mssql-tools/bin/sqlcmd \
        -S localhost -U sa -P YourStrong@Passw0rd \
        -Q "BACKUP DATABASE ruoyi TO DISK = '/tmp/ruoyi_backup.bak'"
    
    docker cp ruoyi-sqlserver:/tmp/ruoyi_backup.bak $backup_dir/
    
    print_message $YELLOW "💾 备份Redis数据..."
    docker cp ruoyi-redis:/data/dump.rdb $backup_dir/
    
    print_message $GREEN "✅ 数据备份完成: $backup_dir"
}

# 显示帮助信息
show_help() {
    echo "若依Go后端部署脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  build       构建Go应用和Docker镜像"
    echo "  dev         部署到开发环境"
    echo "  prod        部署到生产环境"
    echo "  logs        查看应用日志"
    echo "  status      查看服务状态"
    echo "  backup      备份数据"
    echo "  cleanup     清理部署资源"
    echo "  help        显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build     # 构建应用"
    echo "  $0 dev       # 部署开发环境"
    echo "  $0 logs      # 查看日志"
}

# 主函数
main() {
    case "${1:-help}" in
        "build")
            check_environment
            build_go_app
            build_docker_image
            ;;
        "dev")
            check_environment
            build_go_app
            build_docker_image
            deploy_dev
            ;;
        "prod")
            check_environment
            build_go_app
            build_docker_image
            deploy_prod
            ;;
        "logs")
            show_logs
            ;;
        "status")
            show_status
            ;;
        "backup")
            backup_data
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
