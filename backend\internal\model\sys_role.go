package model

// SysRole 角色表 sys_role，对应Java中的SysRole
type SysRole struct {
	BaseModel
	// 角色ID
	RoleID int64 `json:"roleId" gorm:"column:role_id;primary_key;auto_increment;comment:角色ID"`
	// 角色名称
	RoleName string `json:"roleName" gorm:"column:role_name;not null;comment:角色名称" validate:"required,max=30"`
	// 角色权限
	RoleKey string `json:"roleKey" gorm:"column:role_key;not null;comment:角色权限" validate:"required,max=100"`
	// 角色排序
	RoleSort int `json:"roleSort" gorm:"column:role_sort;not null;comment:角色排序" validate:"required"`
	// 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限）
	DataScope string `json:"dataScope" gorm:"column:data_scope;default:1;comment:数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限）"`
	// 菜单树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示）
	MenuCheckStrictly bool `json:"menuCheckStrictly" gorm:"column:menu_check_strictly;default:1;comment:菜单树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示）"`
	// 部门树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示）
	DeptCheckStrictly bool `json:"deptCheckStrictly" gorm:"column:dept_check_strictly;default:1;comment:部门树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示）"`
	// 角色状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status;not null;comment:角色状态（0正常 1停用）"`
	// 删除标志（0代表存在 2代表删除）
	DelFlag string `json:"delFlag" gorm:"column:del_flag;default:0;comment:删除标志（0代表存在 2代表删除）"`

	// 非数据库字段
	// 用户是否存在此角色标识，默认不存在
	Flag bool `json:"flag" gorm:"-"`
	// 菜单组
	MenuIDs []int64 `json:"menuIds" gorm:"-"`
	// 部门组（数据权限）
	DeptIDs []int64 `json:"deptIds" gorm:"-"`
	// 角色菜单权限
	Permissions []string `json:"permissions" gorm:"-"`
}

// TableName 设置表名
func (SysRole) TableName() string {
	return "sys_role"
}

// IsAdmin 判断是否是管理员角色
func (r *SysRole) IsAdmin() bool {
	return IsAdminRole(r.RoleID)
}

// IsAdminRole 判断是否是管理员角色（静态方法）
func IsAdminRole(roleID int64) bool {
	return roleID != 0 && roleID == 1
}

// ToString 返回结构体的字符串表示，类似Java中的toString方法
func (r *SysRole) ToString() string {
	return ""
}
