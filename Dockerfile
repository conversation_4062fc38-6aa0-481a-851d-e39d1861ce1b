# 多阶段构建Dockerfile for 若依Go后端

# 第一阶段：构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的包
RUN apk add --no-cache git gcc musl-dev

# 复制go mod文件
COPY backend/go.mod backend/go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY backend/ .

# 设置环境变量
ENV CGO_ENABLED=1
ENV GOOS=linux
ENV GOARCH=amd64

# 构建应用
RUN go build -ldflags "-w -s" -o ruoyi-go .

# 第二阶段：运行阶段
FROM alpine:latest

# 安装必要的运行时依赖
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非root用户
RUN addgroup -g 1001 -S ruoyi && \
    adduser -u 1001 -S ruoyi -G ruoyi

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/ruoyi-go .

# 复制配置文件
COPY backend/config/ ./config/

# 复制SQL脚本
COPY backend/sql/ ./sql/

# 创建日志目录
RUN mkdir -p logs uploads && \
    chown -R ruoyi:ruoyi /app

# 切换到非root用户
USER ruoyi

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/ || exit 1

# 启动应用
CMD ["./ruoyi-go"]
