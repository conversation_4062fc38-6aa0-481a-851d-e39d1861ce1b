package monitor

import (
	"backend/internal/api/controller"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysOperlogController 操作日志记录
type SysOperlogController struct {
	controller.BaseController
	operLogService service.SysOperLogService
}

// NewSysOperlogController 创建操作日志控制器
func NewSysOperlogController(operLogService service.SysOperLogService) *SysOperlogController {
	return &SysOperlogController{
		operLogService: operLogService,
	}
}

// List 查询操作日志列表
// @Summary 查询操作日志列表
// @Description 查询操作日志列表
// @Tags 操作日志
// @Accept json
// @Produce json
// @Param operLog query model.SysOperLog false "查询参数"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/operlog/list [get]
func (c *SysOperlogController) List(ctx *gin.Context) {
	// 构建查询参数
	operLog := &model.SysOperLog{
		Title:    ctx.Query("title"),
		OperName: ctx.Query("operName"),
		OperIp:   ctx.Query("operIp"),
	}

	// 处理业务类型
	if businessType := ctx.Query("businessType"); businessType != "" {
		businessTypeInt, _ := strconv.Atoi(businessType)
		operLog.BusinessType = businessTypeInt
	}

	// 处理状态
	if status := ctx.Query("status"); status != "" {
		statusInt, _ := strconv.Atoi(status)
		operLog.Status = statusInt
	}

	// 查询操作日志列表
	list, err := c.operLogService.SelectOperLogList(operLog)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, list)
}

// Export 导出操作日志
// @Summary 导出操作日志
// @Description 导出操作日志
// @Tags 操作日志
// @Accept json
// @Produce octet-stream
// @Param operLog query model.SysOperLog false "查询参数"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/operlog/export [post]
func (c *SysOperlogController) Export(ctx *gin.Context) {
	// 导出功能需要实现Excel导出，暂时返回提示信息
	c.Warn(ctx, "导出功能暂未实现")
}

// Remove 删除操作日志
// @Summary 删除操作日志
// @Description 删除操作日志
// @Tags 操作日志
// @Accept json
// @Produce json
// @Param operIds path string true "操作日志ID，多个以逗号分隔"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/operlog/{operIds} [delete]
func (c *SysOperlogController) Remove(ctx *gin.Context) {
	operIdsStr := ctx.Param("operIds")
	operIdStrs := strings.Split(operIdsStr, ",")

	var operIds []int64
	for _, idStr := range operIdStrs {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			c.Error(ctx, utils.NewError("参数错误"))
			return
		}
		operIds = append(operIds, id)
	}

	rows, err := c.operLogService.DeleteOperLogByIds(operIds)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, rows)
}

// Clean 清空操作日志
// @Summary 清空操作日志
// @Description 清空操作日志
// @Tags 操作日志
// @Accept json
// @Produce json
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/operlog/clean [delete]
func (c *SysOperlogController) Clean(ctx *gin.Context) {
	err := c.operLogService.CleanOperLog()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}
