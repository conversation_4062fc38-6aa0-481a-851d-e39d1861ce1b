package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysNoticeRepository 通知公告仓库接口
type SysNoticeRepository interface {
	// SelectNoticeById 查询公告信息
	SelectNoticeById(noticeId int64) (*model.SysNotice, error)

	// SelectNoticeList 查询公告列表
	SelectNoticeList(notice *model.SysNotice) ([]*model.SysNotice, error)

	// InsertNotice 新增公告
	InsertNotice(notice *model.SysNotice) (int64, error)

	// UpdateNotice 修改公告
	UpdateNotice(notice *model.SysNotice) (int64, error)

	// DeleteNoticeById 删除公告信息
	DeleteNoticeById(noticeId int64) error

	// DeleteNoticeByIds 批量删除公告信息
	DeleteNoticeByIds(noticeIds []int64) error
}

// SysNoticeRepositoryImpl 通知公告仓库实现
type SysNoticeRepositoryImpl struct {
	*BaseRepository
}

// NewSysNoticeRepository 创建通知公告仓库
func NewSysNoticeRepository(db *gorm.DB) SysNoticeRepository {
	return &SysNoticeRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// SelectNoticeById 查询公告信息
func (r *SysNoticeRepositoryImpl) SelectNoticeById(noticeId int64) (*model.SysNotice, error) {
	var notice model.SysNotice
	err := r.DB.Where("notice_id = ?", noticeId).First(&notice).Error
	if err != nil {
		return nil, err
	}
	return &notice, nil
}

// SelectNoticeList 查询公告列表
func (r *SysNoticeRepositoryImpl) SelectNoticeList(notice *model.SysNotice) ([]*model.SysNotice, error) {
	var list []*model.SysNotice
	db := r.DB.Model(&model.SysNotice{})

	if notice.NoticeTitle != "" {
		db = db.Where("notice_title LIKE ?", "%"+notice.NoticeTitle+"%")
	}
	if notice.NoticeType != "" {
		db = db.Where("notice_type = ?", notice.NoticeType)
	}
	if notice.CreateBy != "" {
		db = db.Where("create_by = ?", notice.CreateBy)
	}
	if notice.Status != "" {
		db = db.Where("status = ?", notice.Status)
	}

	err := db.Order("create_time DESC").Find(&list).Error
	return list, err
}

// InsertNotice 新增公告
func (r *SysNoticeRepositoryImpl) InsertNotice(notice *model.SysNotice) (int64, error) {
	err := r.DB.Create(notice).Error
	if err != nil {
		return 0, err
	}
	return notice.NoticeID, nil
}

// UpdateNotice 修改公告
func (r *SysNoticeRepositoryImpl) UpdateNotice(notice *model.SysNotice) (int64, error) {
	err := r.DB.Model(&model.SysNotice{}).Where("notice_id = ?", notice.NoticeID).Updates(notice).Error
	if err != nil {
		return 0, err
	}
	return int64(r.DB.RowsAffected), nil
}

// DeleteNoticeById 删除公告信息
func (r *SysNoticeRepositoryImpl) DeleteNoticeById(noticeId int64) error {
	return r.DB.Delete(&model.SysNotice{}, noticeId).Error
}

// DeleteNoticeByIds 批量删除公告信息
func (r *SysNoticeRepositoryImpl) DeleteNoticeByIds(noticeIds []int64) error {
	return r.DB.Delete(&model.SysNotice{}, "notice_id IN ?", noticeIds).Error
}
