package impl

import (
	"backend/internal/constants"
	"backend/internal/model"
	"backend/internal/service"
	"errors"
	"fmt"
	"strings"
)

// SysLoginServiceImpl 登录服务实现
type SysLoginServiceImpl struct {
	configService     service.SysConfigService
	userService       service.SysUserService
	tokenService      service.TokenService
	redisCache        service.RedisCache
	permissionService service.SysPermissionService
}

// NewSysLoginService 创建登录服务
func NewSysLoginService(
	configService service.SysConfigService,
	userService service.SysUserService,
	tokenService service.TokenService,
	redisCache service.RedisCache,
	permissionService service.SysPermissionService,
) service.SysLoginService {
	return &SysLoginServiceImpl{
		configService:     configService,
		userService:       userService,
		tokenService:      tokenService,
		redisCache:        redisCache,
		permissionService: permissionService,
	}
}

// Login 登录
func (s *SysLoginServiceImpl) Login(username, password, code, uuid string) (string, error) {
	// 校验验证码
	captchaEnabled := s.configService.SelectCaptchaEnabled()
	if captchaEnabled {
		err := s.validateCaptcha(username, code, uuid)
		if err != nil {
			return "", err
		}
	}

	// 查询用户
	user, err := s.userService.SelectUserByUserName(username)
	if err != nil {
		return "", fmt.Errorf("用户查询失败: %v", err)
	}

	if user == nil {
		return "", errors.New("登录用户不存在")
	}

	if user.DelFlag == "2" {
		return "", errors.New("您的账号已被删除")
	}

	if user.Status == "1" {
		return "", errors.New("您的账号已停用")
	}

	// 验证密码
	if !s.validatePassword(password, user.Password) {
		return "", errors.New("密码错误")
	}

	// 创建登录用户对象
	loginUser := &model.LoginUser{
		User:        user,
		Permissions: s.permissionService.GetMenuPermission(user),
	}

	// 生成令牌
	token, err := s.tokenService.CreateToken(loginUser)
	if err != nil {
		return "", fmt.Errorf("令牌生成失败: %v", err)
	}

	return token, nil
}

// validateCaptcha 校验验证码
func (s *SysLoginServiceImpl) validateCaptcha(username, code, uuid string) error {
	if code == "" {
		return errors.New("验证码不能为空")
	}
	if uuid == "" {
		return errors.New("验证码已失效")
	}

	verifyKey := constants.CAPTCHA_CODE_KEY + uuid
	captchaObj, err := s.redisCache.GetCacheObject(verifyKey)
	if err != nil || captchaObj == nil {
		return errors.New("验证码已失效")
	}

	captcha, ok := captchaObj.(string)
	if !ok {
		return errors.New("验证码格式错误")
	}

	// 删除验证码
	s.redisCache.DeleteObject(verifyKey)

	// 验证码不区分大小写
	if !strings.EqualFold(code, captcha) {
		return errors.New("验证码错误")
	}

	return nil
}

// validatePassword 验证密码
func (s *SysLoginServiceImpl) validatePassword(password, hashedPassword string) bool {
	// 这里应该使用加密算法验证密码
	// 简单实现，仅用于演示
	return password == hashedPassword
}
