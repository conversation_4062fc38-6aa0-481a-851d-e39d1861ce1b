package service

import "backend/internal/model"

// SysDeptService 部门服务接口
type SysDeptService interface {
	// SelectDeptList 查询部门列表
	SelectDeptList(dept *model.SysDept) ([]*model.SysDept, error)

	// SelectDeptListByRoleId 根据角色ID查询部门树列表
	SelectDeptListByRoleId(roleId int64) ([]int64, error)

	// SelectDeptTreeList 查询部门树结构
	SelectDeptTreeList(dept *model.SysDept) ([]*model.SysDept, error)

	// SelectDeptById 查询部门
	SelectDeptById(deptId int64) (*model.SysDept, error)

	// InsertDept 新增部门
	InsertDept(dept *model.SysDept) (int, error)

	// UpdateDept 修改部门
	UpdateDept(dept *model.SysDept) (int, error)

	// DeleteDeptById 删除部门
	DeleteDeptById(deptId int64) (int, error)

	// HasChildByDeptId 是否存在子部门
	HasChildByDeptId(deptId int64) (bool, error)

	// CheckDeptExistUser 查询部门是否存在用户
	CheckDeptExistUser(deptId int64) (bool, error)

	// CheckDeptNameUnique 校验部门名称是否唯一
	CheckDeptNameUnique(dept *model.SysDept) (bool, error)

	// SelectChildrenDeptById 根据ID查询所有子部门
	SelectChildrenDeptById(deptId int64) ([]*model.SysDept, error)

	// SelectNormalChildrenDeptById 根据ID查询所有正常状态的子部门
	SelectNormalChildrenDeptById(deptId int64) (int64, error)

	// CheckDeptDataScope 校验部门是否有数据权限
	CheckDeptDataScope(deptId int64) error
}
