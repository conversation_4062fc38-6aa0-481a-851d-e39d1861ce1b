package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"backend/internal/model"
	"backend/internal/utils"
)

// Response API响应结构
type Response struct {
	Code    int         `json:"code"` // 状态码
	Message string      `json:"msg"`  // 消息
	Data    interface{} `json:"data"` // 数据
}

// BaseController 基础控制器
type BaseController struct {
}

// Success 返回成功
func (b *BaseController) Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "操作成功",
		Data:    data,
	})
}

// Error 返回错误
func (b *BaseController) Error(c *gin.Context, err error) {
	c.JSO<PERSON>(http.StatusOK, Response{
		Code:    500,
		Message: err.Error(),
		Data:    nil,
	})
}

// Warn 返回警告
func (b *BaseController) Warn(c *gin.Context, message string) {
	c.<PERSON>(http.StatusOK, Response{
		Code:    400,
		Message: message,
		Data:    nil,
	})
}

// Unauthorized 返回未授权
func (b *BaseController) Unauthorized(c *gin.Context, message string) {
	c.JSO<PERSON>(http.StatusOK, Response{
		Code:    401,
		Message: message,
		Data:    nil,
	})
}

// Forbidden 返回禁止访问
func (b *BaseController) Forbidden(c *gin.Context, message string) {
	c.JSON(http.StatusOK, Response{
		Code:    403,
		Message: message,
		Data:    nil,
	})
}

// GetUserId 获取用户ID
func (b *BaseController) GetUserId(c *gin.Context) int64 {
	// 从上下文中获取用户信息
	loginUser, exists := c.Get("loginUser")
	if !exists {
		return 0
	}
	user, ok := loginUser.(*model.LoginUser)
	if !ok || user == nil || user.User == nil {
		return 0
	}
	return user.User.UserID
}

// GetUsername 获取用户名
func (b *BaseController) GetUsername(c *gin.Context) string {
	// 从上下文中获取用户信息
	loginUser, exists := c.Get("loginUser")
	if !exists {
		return ""
	}
	user, ok := loginUser.(*model.LoginUser)
	if !ok || user == nil || user.User == nil {
		return ""
	}
	return user.User.UserName
}

// ToAjax 将操作结果转换为统一返回格式
func (b *BaseController) ToAjax(c *gin.Context, rows int64, err error) {
	if err != nil {
		b.Error(c, err)
		return
	}
	if rows > 0 {
		b.Success(c, rows)
	} else {
		b.Error(c, utils.NewError("操作失败"))
	}
}
