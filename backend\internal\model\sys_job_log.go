package model

import (
	"time"
)

// SysJobLog 定时任务调度日志表 sys_job_log
type SysJobLog struct {
	// 任务日志ID
	JobLogID int64 `json:"jobLogId" gorm:"column:job_log_id;primary_key;auto_increment;comment:任务日志ID"`
	// 任务名称
	JobName string `json:"jobName" gorm:"column:job_name;not null;comment:任务名称"`
	// 任务组名
	JobGroup string `json:"jobGroup" gorm:"column:job_group;not null;comment:任务组名"`
	// 调用目标字符串
	InvokeTarget string `json:"invokeTarget" gorm:"column:invoke_target;not null;comment:调用目标字符串"`
	// 日志信息
	JobMessage string `json:"jobMessage" gorm:"column:job_message;comment:日志信息"`
	// 执行状态（0正常 1失败）
	Status string `json:"status" gorm:"column:status;default:0;comment:执行状态（0正常 1失败）"`
	// 异常信息
	ExceptionInfo string `json:"exceptionInfo" gorm:"column:exception_info;comment:异常信息"`
	// 开始时间
	StartTime *time.Time `json:"startTime" gorm:"column:start_time;comment:开始时间"`
	// 停止时间
	StopTime *time.Time `json:"stopTime" gorm:"column:stop_time;comment:停止时间"`
}

// TableName 设置表名
func (SysJobLog) TableName() string {
	return "sys_job_log"
}
