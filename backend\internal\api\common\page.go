package common

// TableDataInfo 表格分页数据对象，对应Java的TableDataInfo
type TableDataInfo struct {
	// 总记录数
	Total int64 `json:"total"`
	// 列表数据
	Rows interface{} `json:"rows"`
	// 消息状态码
	Code int `json:"code"`
	// 消息内容
	Msg string `json:"msg"`
}

// PageQuery 分页查询参数
type PageQuery struct {
	// 当前页码
	PageNum int `json:"pageNum" form:"pageNum"`
	// 每页记录数
	PageSize int `json:"pageSize" form:"pageSize"`
	// 排序字段
	OrderByColumn string `json:"orderByColumn" form:"orderByColumn"`
	// 排序方向 "asc" 或 "desc"
	IsAsc string `json:"isAsc" form:"isAsc"`
}

// Pagination 分页结构，用于封装分页参数
type Pagination struct {
	// 当前页码
	PageNum int
	// 每页记录数
	PageSize int
	// 排序字段
	OrderByColumn string
	// 排序方向 "asc" 或 "desc"
	IsAsc string
}

// DefaultPageSize 默认页码大小
const DefaultPageSize = 10

// NewTableDataInfo 创建表格分页数据对象
func NewTableDataInfo() *TableDataInfo {
	return &TableDataInfo{}
}

// NewTableDataInfoWithData 创建带数据的表格分页数据对象
func NewTableDataInfoWithData(rows interface{}, total int64) *TableDataInfo {
	return &TableDataInfo{
		Rows:  rows,
		Total: total,
	}
}

// SetTotal 设置总记录数
func (t *TableDataInfo) SetTotal(total int64) {
	t.Total = total
}

// SetRows 设置列表数据
func (t *TableDataInfo) SetRows(rows interface{}) {
	t.Rows = rows
}

// SetCode 设置消息状态码
func (t *TableDataInfo) SetCode(code int) {
	t.Code = code
}

// SetMsg 设置消息内容
func (t *TableDataInfo) SetMsg(msg string) {
	t.Msg = msg
}

// NewPagination 创建分页结构
func NewPagination(pageQuery *PageQuery) *Pagination {
	// 处理默认值
	pageNum := 1
	pageSize := DefaultPageSize

	if pageQuery != nil {
		if pageQuery.PageNum > 0 {
			pageNum = pageQuery.PageNum
		}
		if pageQuery.PageSize > 0 {
			pageSize = pageQuery.PageSize
		}
	}

	return &Pagination{
		PageNum:       pageNum,
		PageSize:      pageSize,
		OrderByColumn: pageQuery.OrderByColumn,
		IsAsc:         pageQuery.IsAsc,
	}
}

// GetOffset 获取分页偏移量
func (p *Pagination) GetOffset() int {
	return (p.PageNum - 1) * p.PageSize
}

// GetLimit 获取分页大小
func (p *Pagination) GetLimit() int {
	return p.PageSize
}

// GetOrderBy 获取排序字符串
func (p *Pagination) GetOrderBy() string {
	if p.OrderByColumn == "" {
		return ""
	}

	// 生成排序字符串，如 "create_time DESC"
	order := "ASC"
	if p.IsAsc == "desc" {
		order = "DESC"
	}

	// 这里需要将驼峰命名转换为下划线命名
	// 简单实现，实际应用中可能需要更复杂的转换
	return p.OrderByColumn + " " + order
}
