package common

import (
	"backend/internal/api/controller"
	"backend/internal/constants"
	"backend/internal/service"
	"backend/internal/utils"
	"log"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/mojocn/base64Captcha"
)

// CaptchaController 验证码操作处理
type CaptchaController struct {
	controller.BaseController
	configService service.SysConfigService
	redisCache    service.RedisCache
}

// NewCaptchaController 创建验证码控制器
func NewCaptchaController(configService service.SysConfigService, redisCache service.RedisCache) *CaptchaController {
	return &CaptchaController{
		configService: configService,
		redisCache:    redisCache,
	}
}

// GetCode 生成验证码
// @Summary 生成验证码
// @Description 生成验证码
// @Tags 通用
// @Accept json
// @Produce json
// @Success 200 {object} controller.Response "成功"
// @Router /captchaImage [get]
func (c *CaptchaController) GetCode(ctx *gin.Context) {
	log.Println("开始生成验证码...")

	// 检查验证码是否启用
	captchaEnabled := c.configService.SelectCaptchaEnabled()
	log.Printf("验证码是否启用: %v", captchaEnabled)

	// 构建结果
	result := make(map[string]interface{})
	result["captchaEnabled"] = captchaEnabled

	// 如果验证码未启用，直接返回
	if !captchaEnabled {
		c.Success(ctx, result)
		return
	}

	// 生成UUID
	uuid := utils.SimpleUUID()
	verifyKey := constants.CAPTCHA_CODE_KEY + uuid
	log.Printf("验证码UUID: %s", uuid)

	// 生成验证码
	captchaType := "math" // 默认使用数学验证码
	log.Printf("验证码类型: %s", captchaType)

	var captchaBase64 string
	var code string

	if captchaType == "math" {
		// 使用自定义数学验证码驱动
		mathDriver := utils.NewCustomMathDriver()

		// 生成验证码ID、问题和答案
		_, question, answer := mathDriver.GenerateIdQuestionAnswer()
		log.Printf("数学验证码: 表达式=%s, 答案=%s", question, answer)

		// 创建验证码
		item, err := mathDriver.DrawCaptcha(question)
		if err != nil {
			log.Printf("生成验证码图片错误: %v", err)
			c.Error(ctx, err)
			return
		}

		captchaBase64 = item.EncodeB64string()
		code = answer

		log.Printf("验证码生成成功，答案: %s", answer)
	} else {
		// 字符验证码
		driver := base64Captcha.NewDriverDigit(60, 160, 4, 0, 0)
		captcha := base64Captcha.NewCaptcha(driver, base64Captcha.DefaultMemStore)
		id, b64s, answer, err := captcha.Generate()
		if err != nil {
			log.Printf("生成验证码错误: %v", err)
			c.Error(ctx, err)
			return
		}

		log.Printf("验证码生成成功，ID: %s, 答案: %s", id, answer)
		captchaBase64 = b64s
		code = answer
	}

	// 将验证码存储到Redis，设置2分钟过期时间
	err := c.redisCache.SetCacheObjectWithExpiration(verifyKey, code, 2*time.Minute)
	if err != nil {
		log.Printf("存储验证码到Redis错误: %v", err)
		c.Error(ctx, err)
		return
	}

	log.Println("验证码已存储到Redis")

	// 构建结果
	result["uuid"] = uuid
	result["img"] = captchaBase64

	// 添加额外的CORS头
	ctx.Header("Access-Control-Allow-Origin", "*")
	ctx.Header("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	ctx.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Accept")

	log.Println("验证码生成完成，准备返回结果")
	c.Success(ctx, result)
}
