package service

import "backend/internal/model"

// SysLogininforService 系统访问日志情况信息服务接口
type SysLogininforService interface {
	// InsertLogininfor 新增系统登录日志
	InsertLogininfor(logininfor *model.SysLogininfor) error

	// SelectLogininforList 查询系统登录日志集合
	SelectLogininforList(logininfor *model.SysLogininfor) ([]*model.SysLogininfor, error)

	// DeleteLogininforByIds 批量删除系统登录日志
	DeleteLogininforByIds(infoIds []int64) (int64, error)

	// CleanLogininfor 清空系统登录日志
	CleanLogininfor() error
}
