package system

import (
	"backend/internal/api/controller"
	"backend/internal/model"
	"backend/internal/service"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysNoticeController 通知公告控制器
type SysNoticeController struct {
	controller.BaseController
	noticeService service.SysNoticeService
}

// NewSysNoticeController 创建通知公告控制器
func NewSysNoticeController(noticeService service.SysNoticeService) *SysNoticeController {
	return &SysNoticeController{
		noticeService: noticeService,
	}
}

// List 获取通知公告列表
// @Router /system/notice/list [get]
func (c *SysNoticeController) List(ctx *gin.Context) {
	var notice model.SysNotice

	// 获取查询参数
	noticeTitle := ctx.Query("noticeTitle")
	if noticeTitle != "" {
		notice.NoticeTitle = noticeTitle
	}

	noticeType := ctx.Query("noticeType")
	if noticeType != "" {
		notice.NoticeType = noticeType
	}

	// 获取分页参数
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	// 查询通知公告列表
	list, err := c.noticeService.SelectNoticeList(&notice)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// TODO: 实现分页
	// 返回数据
	c.Success(ctx, map[string]interface{}{
		"rows":     list,
		"total":    len(list),
		"pageNum":  pageNum,
		"pageSize": pageSize,
	})
}

// GetInfo 根据通知公告编号获取详细信息
// @Router /system/notice/{noticeId} [get]
func (c *SysNoticeController) GetInfo(ctx *gin.Context) {
	noticeIdStr := ctx.Param("noticeId")
	noticeId, err := strconv.ParseInt(noticeIdStr, 10, 64)
	if err != nil {
		c.Warn(ctx, "参数错误")
		return
	}

	notice, err := c.noticeService.SelectNoticeById(noticeId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, notice)
}

// Add 新增通知公告
// @Router /system/notice [post]
func (c *SysNoticeController) Add(ctx *gin.Context) {
	var notice model.SysNotice
	if err := ctx.ShouldBindJSON(&notice); err != nil {
		c.Error(ctx, err)
		return
	}

	// 设置创建者
	notice.CreateBy = c.GetUsername(ctx)

	noticeId, err := c.noticeService.InsertNotice(&notice)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, noticeId, nil)
}

// Edit 修改通知公告
// @Router /system/notice [put]
func (c *SysNoticeController) Edit(ctx *gin.Context) {
	var notice model.SysNotice
	if err := ctx.ShouldBindJSON(&notice); err != nil {
		c.Error(ctx, err)
		return
	}

	// 设置更新者
	notice.UpdateBy = c.GetUsername(ctx)

	rows, err := c.noticeService.UpdateNotice(&notice)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, rows, nil)
}

// Remove 删除通知公告
// @Router /system/notice/{noticeIds} [delete]
func (c *SysNoticeController) Remove(ctx *gin.Context) {
	noticeIdsStr := ctx.Param("noticeIds")
	noticeIds := make([]int64, 0)

	for _, idStr := range strings.Split(noticeIdsStr, ",") {
		if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
			noticeIds = append(noticeIds, id)
		}
	}

	err := c.noticeService.DeleteNoticeByIds(noticeIds)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// RegisterRoutes 注册路由
func (c *SysNoticeController) RegisterRoutes(router *gin.RouterGroup) {
	noticeRouter := router.Group("/system/notice")
	{
		noticeRouter.GET("/list", c.List)
		noticeRouter.GET("/:noticeId", c.GetInfo)
		noticeRouter.POST("", c.Add)
		noticeRouter.PUT("", c.Edit)
		noticeRouter.DELETE("/:noticeIds", c.Remove)
	}
}
