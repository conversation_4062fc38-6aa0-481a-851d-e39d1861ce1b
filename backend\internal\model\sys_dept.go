package model

import "encoding/json"

// SysDept 部门表 sys_dept
type SysDept struct {
	BaseModel
	// 部门ID
	DeptID int64 `json:"deptId" gorm:"column:dept_id;primary_key;auto_increment;comment:部门ID"`
	// 父部门ID
	ParentID int64 `json:"parentId" gorm:"column:parent_id;default:0;comment:父部门ID"`
	// 祖级列表
	Ancestors string `json:"ancestors" gorm:"column:ancestors;comment:祖级列表"`
	// 部门名称
	DeptName string `json:"deptName" gorm:"column:dept_name;not null;comment:部门名称" validate:"required,max=30"`
	// 显示顺序
	OrderNum int `json:"orderNum" gorm:"column:order_num;not null;comment:显示顺序" validate:"required"`
	// 负责人
	Leader string `json:"leader" gorm:"column:leader;comment:负责人"`
	// 联系电话
	Phone string `json:"phone" gorm:"column:phone;comment:联系电话"`
	// 邮箱
	Email string `json:"email" gorm:"column:email;comment:邮箱"`
	// 部门状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status;not null;comment:部门状态（0正常 1停用）"`
	// 删除标志（0代表存在 2代表删除）
	DelFlag string `json:"delFlag" gorm:"column:del_flag;default:0;comment:删除标志（0代表存在 2代表删除）"`

	// 非数据库字段
	// 父部门名称
	ParentName string `json:"parentName" gorm:"-"`
	// 子部门
	Children []*SysDept `json:"children" gorm:"-"`
}

// TableName 设置表名
func (SysDept) TableName() string {
	return "sys_dept"
}

// MarshalJSON 重写MarshalJSON方法，避免循环引用导致JSON序列化失败
func (d *SysDept) MarshalJSON() ([]byte, error) {
	type TempSysDept SysDept
	return json.Marshal((*TempSysDept)(d))
}

// ToString 返回字符串表示
func (d *SysDept) ToString() string {
	return ""
}
