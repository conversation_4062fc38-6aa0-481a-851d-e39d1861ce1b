package system

import (
	"backend/internal/api/common"
	"backend/internal/api/request"
	"backend/internal/constants"
	"backend/internal/model"
	"backend/internal/service"
	"time"

	"github.com/gin-gonic/gin"
)

// SysLoginController 登录验证控制器
type SysLoginController struct {
	common.BaseController
	loginService      service.SysLoginService
	menuService       service.SysMenuService
	permissionService service.SysPermissionService
	tokenService      service.TokenService
	configService     service.SysConfigService
}

// NewSysLoginController 创建登录控制器
func NewSysLoginController(
	loginService service.SysLoginService,
	menuService service.SysMenuService,
	permissionService service.SysPermissionService,
	tokenService service.TokenService,
	configService service.SysConfigService,
) *SysLoginController {
	return &SysLoginController{
		loginService:      loginService,
		menuService:       menuService,
		permissionService: permissionService,
		tokenService:      tokenService,
		configService:     configService,
	}
}

// Login 登录方法
// @Router /login [post]
func (c *SysLoginController) Login(ctx *gin.Context) {
	var loginBody request.LoginBody
	if err := ctx.ShouldBindJSON(&loginBody); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 生成令牌
	token, err := c.loginService.Login(loginBody.Username, loginBody.Password, loginBody.Code, loginBody.UUID)
	if err != nil {
		c.ErrorJSON(ctx, "登录失败: "+err.Error())
		return
	}

	response := common.SuccessResponse()
	response.Put(constants.TOKEN, token)

	c.SuccessJSON(ctx, response)
}

// GetInfo 获取用户信息
// @Router /getInfo [get]
func (c *SysLoginController) GetInfo(ctx *gin.Context) {
	// 获取登录用户
	loginUser := c.GetLoginUser(ctx)
	if loginUser == nil {
		c.ErrorJSON(ctx, "获取用户信息失败")
		return
	}

	user, ok := loginUser.(*model.LoginUser)
	if !ok {
		c.ErrorJSON(ctx, "用户类型错误")
		return
	}

	// 角色集合
	roles := c.permissionService.GetRolePermission(user.User)

	// 权限集合
	permissions := c.permissionService.GetMenuPermission(user.User)

	// 刷新令牌 - 比较权限是否有变化
	shouldRefresh := false
	if len(user.Permissions) != len(permissions) {
		shouldRefresh = true
	} else {
		// 比较每个权限
		permMap := make(map[string]bool)
		for _, p := range permissions {
			permMap[p] = true
		}

		for _, p := range user.Permissions {
			if !permMap[p] {
				shouldRefresh = true
				break
			}
		}
	}

	if shouldRefresh {
		user.Permissions = permissions
		c.tokenService.RefreshToken(user)
	}

	response := common.SuccessResponse()
	response.Put("user", user.User)
	response.Put("roles", roles)
	response.Put("permissions", permissions)
	response.Put("isDefaultModifyPwd", c.initPasswordIsModify(user.User.PwdUpdateDate))
	response.Put("isPasswordExpired", c.passwordIsExpiration(user.User.PwdUpdateDate))

	c.SuccessJSON(ctx, response)
}

// GetRouters 获取路由信息
// @Router /getRouters [get]
func (c *SysLoginController) GetRouters(ctx *gin.Context) {
	userId := c.GetUserId(ctx)

	// 查询菜单树
	menus, err := c.menuService.SelectMenuTreeByUserId(userId)
	if err != nil {
		c.ErrorJSON(ctx, "查询菜单树失败: "+err.Error())
		return
	}

	// 构建前端路由
	routers, err := c.menuService.BuildMenus(menus)
	if err != nil {
		c.ErrorJSON(ctx, "构建前端路由失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, common.SuccessDataResponse(routers))
}

// 检查初始密码是否提醒修改
func (c *SysLoginController) initPasswordIsModify(pwdUpdateDate *time.Time) bool {
	initPasswordModify, err := c.configService.SelectConfigByKey("sys.account.initPasswordModify")
	if err != nil {
		return false
	}

	if initPasswordModify == "1" && pwdUpdateDate == nil {
		return true
	}
	return false
}

// 检查密码是否过期
func (c *SysLoginController) passwordIsExpiration(pwdUpdateDate *time.Time) bool {
	passwordValidateDaysStr, err := c.configService.SelectConfigByKey("sys.account.passwordValidateDays")
	if err != nil {
		return false
	}

	passwordValidateDays := 0
	if passwordValidateDaysStr != "" {
		// 转换为整数
		passwordValidateDays = service.ConvertToInt(passwordValidateDaysStr)
	}

	if passwordValidateDays > 0 {
		if pwdUpdateDate == nil {
			// 如果从未修改过初始密码，直接提醒过期
			return true
		}

		nowDate := time.Now()
		// 计算时间差（天）
		days := int(nowDate.Sub(*pwdUpdateDate).Hours() / 24)
		return days > passwordValidateDays
	}

	return false
}
