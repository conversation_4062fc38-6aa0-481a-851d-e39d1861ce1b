package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysDeptRepository 部门表 Repository接口
type SysDeptRepository interface {
	Repository
	// SelectDeptList 查询部门列表
	SelectDeptList(dept *model.SysDept) ([]*model.SysDept, error)
	// SelectDeptById 根据部门ID查询信息
	SelectDeptById(deptId int64) (*model.SysDept, error)
	// InsertDept 新增部门信息
	InsertDept(dept *model.SysDept) (int64, error)
	// UpdateDept 修改部门信息
	UpdateDept(dept *model.SysDept) error
	// DeleteDeptById 删除部门
	DeleteDeptById(deptId int64) error
	// SelectChildrenDeptById 根据ID查询所有子部门
	SelectChildrenDeptById(deptId int64) ([]*model.SysDept, error)
	// SelectDeptListByRoleId 根据角色ID查询部门树信息
	SelectDeptListByRoleId(roleId int64, deptCheckStrictly bool) ([]int64, error)
	// CheckDeptExistUser 查询部门是否存在用户
	CheckDeptExistUser(deptId int64) (bool, error)
	// HasChildByDeptId 查询部门是否有子部门
	HasChildByDeptId(deptId int64) (bool, error)
	// CheckDeptNameUnique 校验部门名称是否唯一
	CheckDeptNameUnique(dept *model.SysDept) (bool, error)
}

// SysDeptRepositoryImpl 部门表Repository实现
type SysDeptRepositoryImpl struct {
	*BaseRepository
}

// NewSysDeptRepository 创建部门表Repository
func NewSysDeptRepository(db *gorm.DB) SysDeptRepository {
	return &SysDeptRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// SelectDeptList 查询部门列表
func (r *SysDeptRepositoryImpl) SelectDeptList(dept *model.SysDept) ([]*model.SysDept, error) {
	var depts []*model.SysDept
	db := r.DB.Model(&model.SysDept{}).Where("del_flag = '0'")

	if dept.DeptID != 0 {
		db = db.Where("dept_id = ?", dept.DeptID)
	}
	if dept.ParentID != 0 {
		db = db.Where("parent_id = ?", dept.ParentID)
	}
	if dept.DeptName != "" {
		db = db.Where("dept_name LIKE ?", "%"+dept.DeptName+"%")
	}
	if dept.Status != "" {
		db = db.Where("status = ?", dept.Status)
	}

	// 默认按照显示顺序排序
	db = db.Order("parent_id, order_num")

	err := db.Find(&depts).Error
	return depts, err
}

// SelectDeptById 根据部门ID查询信息
func (r *SysDeptRepositoryImpl) SelectDeptById(deptId int64) (*model.SysDept, error) {
	var dept model.SysDept
	err := r.DB.Where("dept_id = ? AND del_flag = '0'", deptId).First(&dept).Error
	if err != nil {
		return nil, err
	}
	return &dept, nil
}

// InsertDept 新增部门信息
func (r *SysDeptRepositoryImpl) InsertDept(dept *model.SysDept) (int64, error) {
	err := r.DB.Create(dept).Error
	if err != nil {
		return 0, err
	}
	return dept.DeptID, nil
}

// UpdateDept 修改部门信息
func (r *SysDeptRepositoryImpl) UpdateDept(dept *model.SysDept) error {
	return r.DB.Updates(dept).Error
}

// DeleteDeptById 删除部门
func (r *SysDeptRepositoryImpl) DeleteDeptById(deptId int64) error {
	return r.DB.Model(&model.SysDept{}).Where("dept_id = ?", deptId).Update("del_flag", "2").Error
}

// SelectChildrenDeptById 根据ID查询所有子部门
func (r *SysDeptRepositoryImpl) SelectChildrenDeptById(deptId int64) ([]*model.SysDept, error) {
	var depts []*model.SysDept
	err := r.DB.Where("find_in_set(?, ancestors) AND del_flag = '0'", deptId).Find(&depts).Error
	return depts, err
}

// SelectDeptListByRoleId 根据角色ID查询部门树信息
func (r *SysDeptRepositoryImpl) SelectDeptListByRoleId(roleId int64, deptCheckStrictly bool) ([]int64, error) {
	var deptIds []int64

	// 不包含子部门
	if deptCheckStrictly {
		err := r.DB.Model(&model.SysDept{}).
			Select("d.dept_id").
			Joins("d, sys_role_dept rd").
			Where("d.dept_id = rd.dept_id AND rd.role_id = ? AND d.del_flag = '0'", roleId).
			Pluck("d.dept_id", &deptIds).Error
		return deptIds, err
	}

	// 包含子部门
	err := r.DB.Model(&model.SysDept{}).
		Select("d.dept_id").
		Joins("d, sys_role_dept rd").
		Where("d.dept_id = rd.dept_id AND rd.role_id = ? AND d.del_flag = '0'", roleId).
		Or("d.parent_id IN (SELECT d.dept_id FROM sys_dept d INNER JOIN sys_role_dept rd ON d.dept_id = rd.dept_id AND rd.role_id = ? WHERE d.del_flag = '0')", roleId).
		Pluck("d.dept_id", &deptIds).Error

	return deptIds, err
}

// CheckDeptExistUser 查询部门是否存在用户
func (r *SysDeptRepositoryImpl) CheckDeptExistUser(deptId int64) (bool, error) {
	var count int64
	err := r.DB.Model(&model.SysUser{}).Where("dept_id = ? AND del_flag = '0'", deptId).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// HasChildByDeptId 查询部门是否有子部门
func (r *SysDeptRepositoryImpl) HasChildByDeptId(deptId int64) (bool, error) {
	var count int64
	err := r.DB.Model(&model.SysDept{}).Where("parent_id = ? AND del_flag = '0'", deptId).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// CheckDeptNameUnique 校验部门名称是否唯一
func (r *SysDeptRepositoryImpl) CheckDeptNameUnique(dept *model.SysDept) (bool, error) {
	var count int64
	db := r.DB.Model(&model.SysDept{}).Where("dept_name = ? AND parent_id = ? AND del_flag = '0'", dept.DeptName, dept.ParentID)

	if dept.DeptID != 0 {
		db = db.Where("dept_id <> ?", dept.DeptID)
	}

	err := db.Count(&count).Error
	if err != nil {
		return false, err
	}
	return count == 0, nil
}
