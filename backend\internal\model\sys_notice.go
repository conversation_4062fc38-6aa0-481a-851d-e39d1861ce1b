package model

// SysNotice 通知公告表 sys_notice
type SysNotice struct {
	BaseModel
	// 公告ID
	NoticeID int64 `json:"noticeId" gorm:"column:notice_id;primary_key;auto_increment;comment:公告ID"`
	// 公告标题
	NoticeTitle string `json:"noticeTitle" gorm:"column:notice_title;comment:公告标题"`
	// 公告类型（1通知 2公告）
	NoticeType string `json:"noticeType" gorm:"column:notice_type;comment:公告类型（1通知 2公告）"`
	// 公告内容
	NoticeContent string `json:"noticeContent" gorm:"column:notice_content;type:longtext;comment:公告内容"`
	// 公告状态（0正常 1关闭）
	Status string `json:"status" gorm:"column:status;default:0;comment:公告状态（0正常 1关闭）"`
}

// TableName 设置表名
func (SysNotice) TableName() string {
	return "sys_notice"
}
