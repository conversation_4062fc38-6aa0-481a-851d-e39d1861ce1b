package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysLogininforRepository 登录日志数据访问接口
type SysLogininforRepository interface {
	// Insert 插入登录日志
	Insert(logininfor *model.SysLogininfor) error

	// SelectList 查询登录日志列表
	SelectList(logininfor *model.SysLogininfor) ([]*model.SysLogininfor, error)

	// DeleteByIds 批量删除登录日志
	DeleteByIds(infoIds []int64) (int64, error)

	// DeleteAll 清空登录日志
	DeleteAll() error
}

// GormSysLogininforRepository 基于Gorm的登录日志数据访问实现
type GormSysLogininforRepository struct {
	db *gorm.DB
}

// NewSysLogininforRepository 创建登录日志数据访问实例
func NewSysLogininforRepository(db *gorm.DB) SysLogininforRepository {
	return &GormSysLogininforRepository{db: db}
}

// Insert 插入登录日志
func (r *GormSysLogininforRepository) Insert(logininfor *model.SysLogininfor) error {
	return r.db.Create(logininfor).Error
}

// SelectList 查询登录日志列表
func (r *GormSysLogininforRepository) SelectList(logininfor *model.SysLogininfor) ([]*model.SysLogininfor, error) {
	var logs []*model.SysLogininfor
	db := r.db.Model(&model.SysLogininfor{})

	// 构建查询条件
	if logininfor != nil {
		if logininfor.UserName != "" {
			db = db.Where("user_name LIKE ?", "%"+logininfor.UserName+"%")
		}
		if logininfor.Status != "" {
			db = db.Where("status = ?", logininfor.Status)
		}
		if logininfor.Ipaddr != "" {
			db = db.Where("ipaddr LIKE ?", "%"+logininfor.Ipaddr+"%")
		}
		if logininfor.LoginTime != nil {
			db = db.Where("login_time >= ?", logininfor.LoginTime)
		}
	}

	// 按访问时间倒序排序
	db = db.Order("login_time DESC")

	err := db.Find(&logs).Error
	return logs, err
}

// DeleteByIds 批量删除登录日志
func (r *GormSysLogininforRepository) DeleteByIds(infoIds []int64) (int64, error) {
	result := r.db.Where("info_id IN ?", infoIds).Delete(&model.SysLogininfor{})
	return result.RowsAffected, result.Error
}

// DeleteAll 清空登录日志
func (r *GormSysLogininforRepository) DeleteAll() error {
	return r.db.Exec("DELETE FROM sys_logininfor").Error
}
