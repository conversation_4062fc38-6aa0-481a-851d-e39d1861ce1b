package repository

import (
	"backend/internal/model"
	"errors"

	"gorm.io/gorm"
)

// SysUserRepository 用户Repository接口
type SysUserRepository interface {
	Repository
	// SelectUserByUserName 根据用户名查询用户
	SelectUserByUserName(userName string) (*model.SysUser, error)
	// SelectUserById 通过用户ID查询用户
	SelectUserById(userId int64) (*model.SysUser, error)
	// InsertUser 新增用户信息
	InsertUser(user *model.SysUser) (int64, error)
	// UpdateUser 修改用户信息
	UpdateUser(user *model.SysUser) error
	// DeleteUserById 通过用户ID删除用户
	DeleteUserById(userId int64) error
	// DeleteUserByIds 批量删除用户信息
	DeleteUserByIds(userIds []int64) error
	// SelectUserList 查询用户列表
	SelectUserList(user *model.SysUser) ([]*model.SysUser, error)
	// SelectAllocatedList 查询已分配用户角色列表
	SelectAllocatedList(user *model.SysUser) ([]*model.SysUser, error)
	// SelectUnallocatedList 查询未分配用户角色列表
	SelectUnallocatedList(user *model.SysUser) ([]*model.SysUser, error)
	// CheckUserNameUnique 校验用户名称是否唯一
	CheckUserNameUnique(userName string) (bool, error)
	// CheckPhoneUnique 校验手机号码是否唯一
	CheckPhoneUnique(user *model.SysUser) (bool, error)
	// CheckEmailUnique 校验email是否唯一
	CheckEmailUnique(user *model.SysUser) (bool, error)
	// ResetUserPwd 重置用户密码
	ResetUserPwd(user *model.SysUser) error
}

// SysUserRepositoryImpl 用户Repository实现
type SysUserRepositoryImpl struct {
	*BaseRepository
}

// NewSysUserRepository 创建用户Repository
func NewSysUserRepository(db *gorm.DB) SysUserRepository {
	return &SysUserRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// SelectUserByUserName 根据用户名查询用户
func (r *SysUserRepositoryImpl) SelectUserByUserName(userName string) (*model.SysUser, error) {
	var user model.SysUser
	err := r.DB.Where("user_name = ? AND del_flag = '0'", userName).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// SelectUserById 通过用户ID查询用户
func (r *SysUserRepositoryImpl) SelectUserById(userId int64) (*model.SysUser, error) {
	var user model.SysUser
	err := r.DB.Where("user_id = ? AND del_flag = '0'", userId).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// InsertUser 新增用户信息
func (r *SysUserRepositoryImpl) InsertUser(user *model.SysUser) (int64, error) {
	err := r.DB.Create(user).Error
	if err != nil {
		return 0, err
	}
	return user.UserID, nil
}

// UpdateUser 修改用户信息
func (r *SysUserRepositoryImpl) UpdateUser(user *model.SysUser) error {
	return r.DB.Updates(user).Error
}

// DeleteUserById 通过用户ID删除用户
func (r *SysUserRepositoryImpl) DeleteUserById(userId int64) error {
	return r.DB.Model(&model.SysUser{}).Where("user_id = ?", userId).Update("del_flag", "2").Error
}

// DeleteUserByIds 批量删除用户信息
func (r *SysUserRepositoryImpl) DeleteUserByIds(userIds []int64) error {
	return r.DB.Model(&model.SysUser{}).Where("user_id IN ?", userIds).Update("del_flag", "2").Error
}

// SelectUserList 查询用户列表
func (r *SysUserRepositoryImpl) SelectUserList(user *model.SysUser) ([]*model.SysUser, error) {
	var users []*model.SysUser
	db := r.DB.Model(&model.SysUser{}).Where("del_flag = '0'")

	if user.UserID != 0 {
		db = db.Where("user_id = ?", user.UserID)
	}
	if user.UserName != "" {
		db = db.Where("user_name LIKE ?", "%"+user.UserName+"%")
	}
	if user.Status != "" {
		db = db.Where("status = ?", user.Status)
	}
	if user.PhoneNumber != "" {
		db = db.Where("phonenumber LIKE ?", "%"+user.PhoneNumber+"%")
	}
	if user.DeptID != 0 {
		db = db.Where("dept_id = ?", user.DeptID)
	}

	err := db.Find(&users).Error
	return users, err
}

// SelectAllocatedList 查询已分配用户角色列表
func (r *SysUserRepositoryImpl) SelectAllocatedList(user *model.SysUser) ([]*model.SysUser, error) {
	var users []*model.SysUser
	db := r.DB.Model(&model.SysUser{}).
		Joins("INNER JOIN sys_user_role ur ON ur.user_id = sys_user.user_id").
		Where("ur.role_id = ? AND sys_user.del_flag = '0'", user.RoleID)

	if user.UserName != "" {
		db = db.Where("sys_user.user_name LIKE ?", "%"+user.UserName+"%")
	}
	if user.PhoneNumber != "" {
		db = db.Where("sys_user.phonenumber LIKE ?", "%"+user.PhoneNumber+"%")
	}

	err := db.Find(&users).Error
	return users, err
}

// SelectUnallocatedList 查询未分配用户角色列表
func (r *SysUserRepositoryImpl) SelectUnallocatedList(user *model.SysUser) ([]*model.SysUser, error) {
	var users []*model.SysUser
	db := r.DB.Model(&model.SysUser{}).
		Where("sys_user.del_flag = '0'").
		Where("sys_user.user_id NOT IN (SELECT ur.user_id FROM sys_user_role ur WHERE ur.role_id = ?)", user.RoleID)

	if user.UserName != "" {
		db = db.Where("sys_user.user_name LIKE ?", "%"+user.UserName+"%")
	}
	if user.PhoneNumber != "" {
		db = db.Where("sys_user.phonenumber LIKE ?", "%"+user.PhoneNumber+"%")
	}

	err := db.Find(&users).Error
	return users, err
}

// CheckUserNameUnique 校验用户名称是否唯一
func (r *SysUserRepositoryImpl) CheckUserNameUnique(userName string) (bool, error) {
	var count int64
	err := r.DB.Model(&model.SysUser{}).Where("user_name = ?", userName).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count == 0, nil
}

// CheckPhoneUnique 校验手机号码是否唯一
func (r *SysUserRepositoryImpl) CheckPhoneUnique(user *model.SysUser) (bool, error) {
	var count int64
	db := r.DB.Model(&model.SysUser{}).Where("phonenumber = ?", user.PhoneNumber)
	if user.UserID != 0 {
		db = db.Where("user_id <> ?", user.UserID)
	}
	err := db.Count(&count).Error
	if err != nil {
		return false, err
	}
	return count == 0, nil
}

// CheckEmailUnique 校验email是否唯一
func (r *SysUserRepositoryImpl) CheckEmailUnique(user *model.SysUser) (bool, error) {
	var count int64
	db := r.DB.Model(&model.SysUser{}).Where("email = ?", user.Email)
	if user.UserID != 0 {
		db = db.Where("user_id <> ?", user.UserID)
	}
	err := db.Count(&count).Error
	if err != nil {
		return false, err
	}
	return count == 0, nil
}

// ResetUserPwd 重置用户密码
func (r *SysUserRepositoryImpl) ResetUserPwd(user *model.SysUser) error {
	return r.DB.Model(&model.SysUser{}).
		Where("user_id = ?", user.UserID).
		Update("password", user.Password).Error
}
