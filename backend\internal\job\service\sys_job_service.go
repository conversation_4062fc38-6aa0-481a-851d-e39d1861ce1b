package service

import (
	"backend/internal/model"
)

// SysJobService 定时任务调度信息信息服务接口
type SysJobService interface {
	// SelectJobList 获取quartz调度器的计划任务列表
	SelectJobList(job *model.SysJob) ([]*model.SysJob, error)

	// SelectJobById 通过调度任务ID查询调度信息
	SelectJobById(jobId int64) (*model.SysJob, error)

	// PauseJob 暂停任务
	PauseJob(job *model.SysJob) (int, error)

	// ResumeJob 恢复任务
	ResumeJob(job *model.SysJob) (int, error)

	// DeleteJob 删除任务
	DeleteJob(job *model.SysJob) (int, error)

	// DeleteJobByIds 批量删除调度信息
	DeleteJobByIds(jobIds []int64) error

	// ChangeStatus 任务调度状态修改
	ChangeStatus(job *model.SysJob) (int, error)

	// Run 立即运行任务
	Run(job *model.SysJob) (bool, error)

	// InsertJob 新增任务
	InsertJob(job *model.SysJob) (int, error)

	// UpdateJob 更新任务
	UpdateJob(job *model.SysJob) (int, error)

	// CheckCronExpressionIsValid 校验cron表达式是否有效
	CheckCronExpressionIsValid(cronExpression string) bool
}
