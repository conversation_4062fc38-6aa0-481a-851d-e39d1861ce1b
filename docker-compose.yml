version: '3.8'

services:
  # SQL Server数据库
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: ruoyi-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
      - ./backend/sql:/docker-entrypoint-initdb.d
    networks:
      - ruoyi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Passw0rd -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ruoyi-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - ruoyi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Go后端应用
  ruoyi-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ruoyi-go-backend
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
      - DB_HOST=sqlserver
      - DB_PORT=1433
      - DB_USER=sa
      - DB_PASSWORD=YourStrong@Passw0rd
      - DB_NAME=ruoyi
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./backend/config:/app/config
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    depends_on:
      sqlserver:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ruoyi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: ruoyi-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      - ruoyi-backend
    networks:
      - ruoyi-network
    restart: unless-stopped

volumes:
  sqlserver_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local

networks:
  ruoyi-network:
    driver: bridge
