package impl

import (
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
)

// SysLogininforServiceImpl 登录日志服务实现
type SysLogininforServiceImpl struct {
	logininforRepository repository.SysLogininforRepository
}

// NewSysLogininforService 创建登录日志服务实例
func NewSysLogininforService(logininforRepository repository.SysLogininforRepository) service.SysLogininforService {
	return &SysLogininforServiceImpl{
		logininforRepository: logininforRepository,
	}
}

// InsertLogininfor 新增系统登录日志
func (s *SysLogininforServiceImpl) InsertLogininfor(logininfor *model.SysLogininfor) error {
	return s.logininforRepository.Insert(logininfor)
}

// SelectLogininforList 查询系统登录日志集合
func (s *SysLogininforServiceImpl) SelectLogininforList(logininfor *model.SysLogininfor) ([]*model.SysLogininfor, error) {
	return s.logininforRepository.SelectList(logininfor)
}

// DeleteLogininforByIds 批量删除系统登录日志
func (s *SysLogininforServiceImpl) DeleteLogininforByIds(infoIds []int64) (int64, error) {
	return s.logininforRepository.DeleteByIds(infoIds)
}

// CleanLogininfor 清空系统登录日志
func (s *SysLogininforServiceImpl) CleanLogininfor() error {
	return s.logininforRepository.DeleteAll()
}
