package impl

import (
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"backend/internal/utils"
	"fmt"
)

// SysRoleServiceImpl 角色服务实现
type SysRoleServiceImpl struct {
	roleRepository     repository.SysRoleRepository
	roleMenuRepository repository.SysRoleMenuRepository
	roleDeptRepository repository.SysRoleDeptRepository
	userRoleRepository repository.SysUserRoleRepository
}

// NewSysRoleService 创建角色服务实例
func NewSysRoleService(
	roleRepository repository.SysRoleRepository,
	roleMenuRepository repository.SysRoleMenuRepository,
	roleDeptRepository repository.SysRoleDeptRepository,
	userRoleRepository repository.SysUserRoleRepository,
) service.SysRoleService {
	return &SysRoleServiceImpl{
		roleRepository:     roleRepository,
		roleMenuRepository: roleMenuRepository,
		roleDeptRepository: roleDeptRepository,
		userRoleRepository: userRoleRepository,
	}
}

// SelectRoleList 根据条件分页查询角色数据
func (s *SysRoleServiceImpl) SelectRoleList(role *model.SysRole) ([]*model.SysRole, error) {
	return s.roleRepository.SelectRoleList(role)
}

// SelectRoleById 通过角色ID查询角色
func (s *SysRoleServiceImpl) SelectRoleById(roleId int64) (*model.SysRole, error) {
	return s.roleRepository.SelectRoleById(roleId)
}

// SelectRoleAll 查询所有角色
func (s *SysRoleServiceImpl) SelectRoleAll() ([]*model.SysRole, error) {
	return s.SelectRoleList(&model.SysRole{})
}

// SelectRolesByUserId 根据用户ID查询角色
func (s *SysRoleServiceImpl) SelectRolesByUserId(userId int64) ([]*model.SysRole, error) {
	// 查询用户拥有的角色
	userRoles, err := s.roleRepository.SelectRolesByUserId(userId)
	if err != nil {
		return nil, err
	}

	// 查询所有角色
	roles, err := s.SelectRoleAll()
	if err != nil {
		return nil, err
	}

	// 标记用户拥有的角色
	for _, role := range roles {
		for _, userRole := range userRoles {
			if role.RoleID == userRole.RoleID {
				role.Flag = true
				break
			}
		}
	}

	return roles, nil
}

// InsertRole 新增角色
func (s *SysRoleServiceImpl) InsertRole(role *model.SysRole) (int, error) {
	// 新增角色信息
	roleId, err := s.roleRepository.InsertRole(role)
	if err != nil {
		return 0, err
	}
	role.RoleID = roleId

	// 新增角色菜单关联
	err = s.insertRoleMenu(role)
	if err != nil {
		return 0, err
	}

	return 1, nil
}

// UpdateRole 修改角色
func (s *SysRoleServiceImpl) UpdateRole(role *model.SysRole) (int, error) {
	// 修改角色信息
	err := s.roleRepository.UpdateRole(role)
	if err != nil {
		return 0, err
	}

	// 删除角色与菜单关联
	err = s.roleMenuRepository.DeleteRoleMenuByRoleId(role.RoleID)
	if err != nil {
		return 0, err
	}

	// 新增角色菜单关联
	err = s.insertRoleMenu(role)
	if err != nil {
		return 0, err
	}

	return 1, nil
}

// AuthDataScope 修改数据权限
func (s *SysRoleServiceImpl) AuthDataScope(role *model.SysRole) (int, error) {
	// 修改角色信息
	err := s.roleRepository.UpdateRole(role)
	if err != nil {
		return 0, err
	}

	// 删除角色与部门关联
	err = s.roleDeptRepository.DeleteRoleDeptByRoleId(role.RoleID)
	if err != nil {
		return 0, err
	}

	// 新增角色部门信息
	err = s.insertRoleDept(role)
	if err != nil {
		return 0, err
	}

	return 1, nil
}

// UpdateRoleStatus 修改角色状态
func (s *SysRoleServiceImpl) UpdateRoleStatus(role *model.SysRole) (int, error) {
	err := s.roleRepository.UpdateRole(role)
	if err != nil {
		return 0, err
	}
	return 1, nil
}

// DeleteRoleById 删除角色
func (s *SysRoleServiceImpl) DeleteRoleById(roleId int64) (int, error) {
	// 删除角色与菜单关联
	err := s.roleMenuRepository.DeleteRoleMenuByRoleId(roleId)
	if err != nil {
		return 0, err
	}

	// 删除角色与部门关联
	err = s.roleDeptRepository.DeleteRoleDeptByRoleId(roleId)
	if err != nil {
		return 0, err
	}

	// 删除角色
	err = s.roleRepository.DeleteRoleById(roleId)
	if err != nil {
		return 0, err
	}

	return 1, nil
}

// DeleteRoleByIds 批量删除角色
func (s *SysRoleServiceImpl) DeleteRoleByIds(roleIds []int64) (int, error) {
	for _, roleId := range roleIds {
		role := &model.SysRole{RoleID: roleId}
		err := s.CheckRoleAllowed(role)
		if err != nil {
			return 0, err
		}

		err = s.CheckRoleDataScope(roleId)
		if err != nil {
			return 0, err
		}

		// 查询角色信息
		role, err = s.SelectRoleById(roleId)
		if err != nil {
			return 0, err
		}

		// 检查是否已分配用户
		count, err := s.userRoleRepository.CountUserRoleByRoleId(roleId)
		if err != nil {
			return 0, err
		}
		if count > 0 {
			return 0, utils.NewError(fmt.Sprintf("%s已分配,不能删除", role.RoleName))
		}
	}

	// 删除角色与菜单关联
	err := s.roleMenuRepository.DeleteRoleMenu(roleIds)
	if err != nil {
		return 0, err
	}

	// 删除角色与部门关联
	err = s.roleDeptRepository.DeleteRoleDept(roleIds)
	if err != nil {
		return 0, err
	}

	// 删除角色
	err = s.roleRepository.DeleteRoleByIds(roleIds)
	if err != nil {
		return 0, err
	}

	return 1, nil
}

// CheckRoleNameUnique 校验角色名称是否唯一
func (s *SysRoleServiceImpl) CheckRoleNameUnique(role *model.SysRole) bool {
	unique, err := s.roleRepository.CheckRoleNameUnique(role)
	if err != nil {
		return false
	}
	return unique
}

// CheckRoleKeyUnique 校验角色权限是否唯一
func (s *SysRoleServiceImpl) CheckRoleKeyUnique(role *model.SysRole) bool {
	unique, err := s.roleRepository.CheckRoleKeyUnique(role)
	if err != nil {
		return false
	}
	return unique
}

// CheckRoleAllowed 校验角色是否允许操作
func (s *SysRoleServiceImpl) CheckRoleAllowed(role *model.SysRole) error {
	if role != nil && role.RoleID > 0 && role.IsAdmin() {
		return utils.NewError("不允许操作超级管理员角色")
	}
	return nil
}

// CheckRoleDataScope 校验角色是否有数据权限
func (s *SysRoleServiceImpl) CheckRoleDataScope(roleId int64) error {
	// 如果是管理员则不需要校验
	if utils.IsAdmin(utils.GetUserId(utils.GetLoginUser())) {
		return nil
	}

	// 查询角色是否存在
	role := &model.SysRole{RoleID: roleId}
	roles, err := s.SelectRoleList(role)
	if err != nil {
		return err
	}

	if len(roles) == 0 {
		return utils.NewError("没有权限访问角色数据！")
	}

	return nil
}

// CheckRoleDataScopeByIds 校验角色是否有数据权限（批量）
func (s *SysRoleServiceImpl) CheckRoleDataScopeByIds(roleIds []int64) error {
	if roleIds == nil || len(roleIds) == 0 {
		return nil
	}

	for _, roleId := range roleIds {
		err := s.CheckRoleDataScope(roleId)
		if err != nil {
			return err
		}
	}

	return nil
}

// DeleteAuthUser 取消授权用户角色
func (s *SysRoleServiceImpl) DeleteAuthUser(userRole *model.SysUserRole) (int, error) {
	// 删除用户与角色关联
	err := s.userRoleRepository.DeleteUserRoleInfo(userRole)
	if err != nil {
		return 0, err
	}
	return 1, nil
}

// DeleteAuthUsers 批量取消授权用户角色
func (s *SysRoleServiceImpl) DeleteAuthUsers(roleId int64, userIds []int64) (int, error) {
	// 删除用户与角色关联
	err := s.userRoleRepository.DeleteUserRoleInfos(roleId, userIds)
	if err != nil {
		return 0, err
	}
	return 1, nil
}

// InsertAuthUsers 批量选择授权用户角色
func (s *SysRoleServiceImpl) InsertAuthUsers(roleId int64, userIds []int64) (int, error) {
	// 检查角色数据权限
	err := s.CheckRoleDataScope(roleId)
	if err != nil {
		return 0, err
	}

	// 新增用户与角色关联
	list := make([]*model.SysUserRole, 0, len(userIds))
	for _, userId := range userIds {
		userRole := &model.SysUserRole{
			UserID: userId,
			RoleID: roleId,
		}
		list = append(list, userRole)
	}
	err = s.userRoleRepository.BatchUserRole(list)
	if err != nil {
		return 0, err
	}
	return 1, nil
}

// insertRoleMenu 新增角色菜单信息
func (s *SysRoleServiceImpl) insertRoleMenu(role *model.SysRole) error {
	if role.MenuIDs == nil || len(role.MenuIDs) == 0 {
		return nil
	}

	// 新增用户与角色管理
	list := make([]*model.SysRoleMenu, 0, len(role.MenuIDs))
	for _, menuId := range role.MenuIDs {
		roleMenu := &model.SysRoleMenu{
			RoleID: role.RoleID,
			MenuID: menuId,
		}
		list = append(list, roleMenu)
	}

	err := s.roleMenuRepository.BatchRoleMenu(list)
	return err
}

// insertRoleDept 新增角色部门信息
func (s *SysRoleServiceImpl) insertRoleDept(role *model.SysRole) error {
	if role.DeptIDs == nil || len(role.DeptIDs) == 0 {
		return nil
	}

	// 新增角色与部门管理
	list := make([]*model.SysRoleDept, 0, len(role.DeptIDs))
	for _, deptId := range role.DeptIDs {
		roleDept := &model.SysRoleDept{
			RoleID: role.RoleID,
			DeptID: deptId,
		}
		list = append(list, roleDept)
	}

	err := s.roleDeptRepository.BatchRoleDept(list)
	return err
}
