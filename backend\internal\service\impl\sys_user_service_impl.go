package impl

import (
	"backend/internal/model"
	"backend/internal/repository"
	"backend/internal/service"
	"backend/internal/utils"
	"strings"
)

// SysUserServiceImpl 用户服务实现
type SysUserServiceImpl struct {
	userRepo     repository.SysUserRepository
	roleRepo     repository.SysRoleRepository
	postRepo     repository.SysPostRepository
	userRoleRepo repository.SysUserRoleRepository
	userPostRepo repository.SysUserPostRepository
	configRepo   repository.SysConfigRepository
	deptRepo     repository.SysDeptRepository
}

// NewSysUserService 创建用户服务
func NewSysUserService(
	userRepo repository.SysUserRepository,
	roleRepo repository.SysRoleRepository,
	postRepo repository.SysPostRepository,
	userRoleRepo repository.SysUserRoleRepository,
	userPostRepo repository.SysUserPostRepository,
	configRepo repository.SysConfigRepository,
	deptRepo repository.SysDeptRepository,
) service.SysUserService {
	return &SysUserServiceImpl{
		userRepo:     userRepo,
		roleRepo:     roleRepo,
		postRepo:     postRepo,
		userRoleRepo: userRoleRepo,
		userPostRepo: userPostRepo,
		configRepo:   configRepo,
		deptRepo:     deptRepo,
	}
}

// SelectUserList 根据条件分页查询用户列表
func (s *SysUserServiceImpl) SelectUserList(user *model.SysUser) ([]*model.SysUser, error) {
	return s.userRepo.SelectUserList(user)
}

// SelectAllocatedList 根据条件分页查询已分配用户角色列表
func (s *SysUserServiceImpl) SelectAllocatedList(user *model.SysUser) ([]*model.SysUser, error) {
	return s.userRepo.SelectAllocatedList(user)
}

// SelectUnallocatedList 根据条件分页查询未分配用户角色列表
func (s *SysUserServiceImpl) SelectUnallocatedList(user *model.SysUser) ([]*model.SysUser, error) {
	return s.userRepo.SelectUnallocatedList(user)
}

// SelectUserByUserName 通过用户名查询用户
func (s *SysUserServiceImpl) SelectUserByUserName(userName string) (*model.SysUser, error) {
	return s.userRepo.SelectUserByUserName(userName)
}

// SelectUserById 通过用户ID查询用户
func (s *SysUserServiceImpl) SelectUserById(userId int64) (*model.SysUser, error) {
	return s.userRepo.SelectUserById(userId)
}

// SelectUserRoleGroup 根据用户ID查询用户所属角色组
func (s *SysUserServiceImpl) SelectUserRoleGroup(userName string) (string, error) {
	roles, err := s.roleRepo.SelectRolesByUserName(userName)
	if err != nil {
		return "", err
	}

	if len(roles) == 0 {
		return "", nil
	}

	roleNames := make([]string, 0, len(roles))
	for _, role := range roles {
		roleNames = append(roleNames, role.RoleName)
	}

	return strings.Join(roleNames, ","), nil
}

// SelectUserPostGroup 根据用户ID查询用户所属岗位组
func (s *SysUserServiceImpl) SelectUserPostGroup(userName string) (string, error) {
	posts, err := s.postRepo.SelectPostsByUserName(userName)
	if err != nil {
		return "", err
	}

	if len(posts) == 0 {
		return "", nil
	}

	postNames := make([]string, 0, len(posts))
	for _, post := range posts {
		postNames = append(postNames, post.PostName)
	}

	return strings.Join(postNames, ","), nil
}

// CheckUserNameUnique 校验用户名称是否唯一
func (s *SysUserServiceImpl) CheckUserNameUnique(user *model.SysUser) bool {
	isUnique, err := s.userRepo.CheckUserNameUnique(user.UserName)
	if err != nil {
		return false
	}
	return isUnique
}

// CheckPhoneUnique 校验手机号码是否唯一
func (s *SysUserServiceImpl) CheckPhoneUnique(user *model.SysUser) bool {
	isUnique, err := s.userRepo.CheckPhoneUnique(user)
	if err != nil {
		return false
	}
	return isUnique
}

// CheckEmailUnique 校验email是否唯一
func (s *SysUserServiceImpl) CheckEmailUnique(user *model.SysUser) bool {
	isUnique, err := s.userRepo.CheckEmailUnique(user)
	if err != nil {
		return false
	}
	return isUnique
}

// CheckUserAllowed 校验用户是否允许操作
func (s *SysUserServiceImpl) CheckUserAllowed(user *model.SysUser) error {
	if user.IsAdmin() {
		return utils.NewError("不允许操作超级管理员用户")
	}
	return nil
}

// CheckUserDataScope 校验用户是否有数据权限
func (s *SysUserServiceImpl) CheckUserDataScope(userId int64) error {
	// 暂时不实现数据权限检查，返回nil表示有权限
	return nil
}

// InsertUser 新增用户信息
func (s *SysUserServiceImpl) InsertUser(user *model.SysUser) (int64, error) {
	// 加密密码
	user.Password = utils.EncryptPassword(user.Password)

	// 新增用户信息
	userId, err := s.userRepo.InsertUser(user)
	if err != nil {
		return 0, err
	}

	// 新增用户与角色关联
	if len(user.RoleIDs) > 0 {
		err = s.InsertUserAuth(userId, user.RoleIDs)
		if err != nil {
			return 0, err
		}
	}

	// 新增用户与岗位关联
	if len(user.PostIDs) > 0 {
		err = s.userPostRepo.InsertUserPost(userId, user.PostIDs)
		if err != nil {
			return 0, err
		}
	}

	return userId, nil
}

// RegisterUser 注册用户信息
func (s *SysUserServiceImpl) RegisterUser(user *model.SysUser) bool {
	// 加密密码
	user.Password = utils.EncryptPassword(user.Password)

	// 注册用户
	_, err := s.userRepo.InsertUser(user)
	if err != nil {
		return false
	}

	return true
}

// UpdateUser 修改用户信息
func (s *SysUserServiceImpl) UpdateUser(user *model.SysUser) int {
	userId := user.UserID

	// 修改用户信息
	err := s.userRepo.UpdateUser(user)
	if err != nil {
		return 0
	}

	// 删除用户与角色关联
	err = s.userRoleRepo.DeleteUserRoleByUserId(userId)
	if err != nil {
		return 0
	}

	// 新增用户与角色关联
	if len(user.RoleIDs) > 0 {
		err = s.InsertUserAuth(userId, user.RoleIDs)
		if err != nil {
			return 0
		}
	}

	// 删除用户与岗位关联
	err = s.userPostRepo.DeleteUserPostByUserId(userId)
	if err != nil {
		return 0
	}

	// 新增用户与岗位关联
	if len(user.PostIDs) > 0 {
		err = s.userPostRepo.InsertUserPost(userId, user.PostIDs)
		if err != nil {
			return 0
		}
	}

	return 1
}

// InsertUserAuth 用户授权角色
func (s *SysUserServiceImpl) InsertUserAuth(userId int64, roleIds []int64) error {
	// 先删除用户与角色关联
	err := s.userRoleRepo.DeleteUserRoleByUserId(userId)
	if err != nil {
		return err
	}

	// 新增用户与角色关联
	return s.userRoleRepo.InsertUserRole(userId, roleIds)
}

// UpdateUserStatus 修改用户状态
func (s *SysUserServiceImpl) UpdateUserStatus(user *model.SysUser) int {
	err := s.userRepo.UpdateUser(user)
	if err != nil {
		return 0
	}
	return 1
}

// UpdateUserProfile 修改用户基本信息
func (s *SysUserServiceImpl) UpdateUserProfile(user *model.SysUser) int {
	err := s.userRepo.UpdateUser(user)
	if err != nil {
		return 0
	}
	return 1
}

// UpdateUserAvatar 修改用户头像
func (s *SysUserServiceImpl) UpdateUserAvatar(userId int64, avatar string) bool {
	user := &model.SysUser{
		UserID: userId,
		Avatar: avatar,
	}
	err := s.userRepo.UpdateUser(user)
	if err != nil {
		return false
	}
	return true
}

// ResetPwd 重置用户密码
func (s *SysUserServiceImpl) ResetPwd(user *model.SysUser) int {
	// 加密密码
	user.Password = utils.EncryptPassword(user.Password)

	err := s.userRepo.ResetUserPwd(user)
	if err != nil {
		return 0
	}
	return 1
}

// ResetUserPwd 重置用户密码
func (s *SysUserServiceImpl) ResetUserPwd(userId int64, password string) int {
	// 加密密码
	encryptedPassword := utils.EncryptPassword(password)

	user := &model.SysUser{
		UserID:   userId,
		Password: encryptedPassword,
	}

	err := s.userRepo.ResetUserPwd(user)
	if err != nil {
		return 0
	}
	return 1
}

// DeleteUserById 通过用户ID删除用户
func (s *SysUserServiceImpl) DeleteUserById(userId int64) int {
	// 删除用户与角色关联
	err := s.userRoleRepo.DeleteUserRoleByUserId(userId)
	if err != nil {
		return 0
	}

	// 删除用户与岗位关联
	err = s.userPostRepo.DeleteUserPostByUserId(userId)
	if err != nil {
		return 0
	}

	// 删除用户
	err = s.userRepo.DeleteUserById(userId)
	if err != nil {
		return 0
	}

	return 1
}

// DeleteUserByIds 批量删除用户信息
func (s *SysUserServiceImpl) DeleteUserByIds(userIds []int64) int {
	for _, userId := range userIds {
		// 检查是否为管理员
		if model.IsAdminUser(userId) {
			return 0
		}

		// 删除用户与角色关联
		err := s.userRoleRepo.DeleteUserRoleByUserId(userId)
		if err != nil {
			return 0
		}

		// 删除用户与岗位关联
		err = s.userPostRepo.DeleteUserPostByUserId(userId)
		if err != nil {
			return 0
		}
	}

	// 批量删除用户
	err := s.userRepo.DeleteUserByIds(userIds)
	if err != nil {
		return 0
	}

	return 1
}

// ImportUser 导入用户数据
func (s *SysUserServiceImpl) ImportUser(userList []*model.SysUser, isUpdateSupport bool, operName string) string {
	successNum := 0
	failureNum := 0
	successMsg := ""
	failureMsg := ""

	// 默认密码
	config, err := s.configRepo.SelectConfigByKey("sys.user.initPassword")
	password := "123456" // 默认密码
	if err == nil && config != nil {
		password = config.ConfigValue
	}

	for i, user := range userList {
		// 验证是否存在这个用户
		existUser, err := s.SelectUserByUserName(user.UserName)
		if err != nil {
			failureNum++
			failureMsg += "第" + string(i+1) + "行数据导入失败：" + err.Error() + "<br/>"
			continue
		}

		if existUser != nil {
			// 如果用户存在但不允许更新，则跳过
			if !isUpdateSupport {
				failureNum++
				failureMsg += "第" + string(i+1) + "行数据导入失败，用户名 " + user.UserName + " 已存在。<br/>"
				continue
			}

			user.UserID = existUser.UserID
			user.Password = existUser.Password
			user.CreateBy = existUser.CreateBy
		} else {
			user.Password = utils.EncryptPassword(password)
			user.CreateBy = operName
		}

		if existUser != nil {
			// 更新用户信息
			err = s.userRepo.UpdateUser(user)
			if err != nil {
				failureNum++
				failureMsg += "第" + string(i+1) + "行数据导入失败：" + err.Error() + "<br/>"
			} else {
				successNum++
				successMsg += "第" + string(i+1) + "行数据更新成功。<br/>"
			}
		} else {
			// 新增用户信息
			_, err = s.userRepo.InsertUser(user)
			if err != nil {
				failureNum++
				failureMsg += "第" + string(i+1) + "行数据导入失败：" + err.Error() + "<br/>"
			} else {
				successNum++
				successMsg += "第" + string(i+1) + "行数据导入成功。<br/>"
			}
		}
	}

	if successNum > 0 {
		successMsg = "成功导入 " + string(successNum) + " 条数据。<br/>" + successMsg
	}

	if failureNum > 0 {
		failureMsg = "失败 " + string(failureNum) + " 条数据。<br/>" + failureMsg
	}

	return successMsg + failureMsg
}
