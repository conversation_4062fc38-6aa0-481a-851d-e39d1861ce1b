package service

import "backend/internal/model"

// TokenService 令牌服务接口
type TokenService interface {
	// CreateToken 创建令牌
	CreateToken(loginUser *model.LoginUser) (string, error)
	// GetLoginUser 获取登录用户
	GetLoginUser(token string) (*model.LoginUser, error)
	// DelLoginUser 删除登录用户
	DelLoginUser(token string) error
	// RefreshToken 刷新令牌
	RefreshToken(loginUser *model.LoginUser) error
	// IsTokenExpired 判断令牌是否过期
	IsTokenExpired(loginUser *model.LoginUser) bool
	// VerifyToken 验证令牌有效期，相差不足20分钟，自动刷新缓存
	VerifyToken(loginUser *model.LoginUser) error
}
