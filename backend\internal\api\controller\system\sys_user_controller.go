package system

import (
	"backend/internal/api/common"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysUserController 用户信息控制器
type SysUserController struct {
	common.BaseController
	userService service.SysUserService
	roleService service.SysRoleService
	deptService service.SysDeptService
	postService service.SysPostService
}

// NewSysUserController 创建用户控制器
func NewSysUserController(
	userService service.SysUserService,
	roleService service.SysRoleService,
	deptService service.SysDeptService,
	postService service.SysPostService,
) *SysUserController {
	return &SysUserController{
		userService: userService,
		roleService: roleService,
		deptService: deptService,
		postService: postService,
	}
}

// List 获取用户列表
// @Router /system/user/list [get]
func (c *SysUserController) List(ctx *gin.Context) {
	// 绑定查询参数
	user := &model.SysUser{}
	if userName := ctx.Query("userName"); userName != "" {
		user.UserName = userName
	}
	if nickName := ctx.Query("nickName"); nickName != "" {
		user.NickName = nickName
	}
	if status := ctx.Query("status"); status != "" {
		user.Status = status
	}
	if deptIdStr := ctx.Query("deptId"); deptIdStr != "" {
		if deptId, err := strconv.ParseInt(deptIdStr, 10, 64); err == nil {
			user.DeptID = deptId
		}
	}
	if phonenumber := ctx.Query("phonenumber"); phonenumber != "" {
		user.PhoneNumber = phonenumber
	}

	// 设置分页
	_ = c.StartPage(ctx) // 忽略返回值，暂未实现分页

	// 查询用户列表
	users, err := c.userService.SelectUserList(user)
	if err != nil {
		c.ErrorJSON(ctx, "查询用户列表失败: "+err.Error())
		return
	}

	// TODO: 实现分页查询
	total := int64(len(users))

	// 返回分页数据
	tableData := c.GetDataTable(users, total)
	c.SuccessJSON(ctx, tableData)
}

// GetInfo 根据用户编号获取详细信息
// @Router /system/user/{userId} [get]
func (c *SysUserController) GetInfo(ctx *gin.Context) {
	userId := ctx.Param("userId")
	response := common.SuccessResponse()

	if userId != "" && userId != "0" {
		// 检查数据权限
		id, _ := strconv.ParseInt(userId, 10, 64)
		c.userService.CheckUserDataScope(id)

		// 查询用户信息
		user, err := c.userService.SelectUserById(id)
		if err != nil {
			c.ErrorJSON(ctx, "查询用户信息失败: "+err.Error())
			return
		}

		// 查询用户岗位
		// TODO: 实现查询用户岗位
		// postIds := c.postService.SelectPostListByUserId(id)

		// 查询用户角色ID
		// TODO: 实现查询用户角色
		// roleIds := getRoleIds(user.Roles)

		response.Put(common.DataTag, user)
		// response.Put("postIds", postIds)
		// response.Put("roleIds", roleIds)
	}

	// 查询所有角色
	roles, err := c.roleService.SelectRoleAll()
	if err != nil {
		c.ErrorJSON(ctx, "查询角色列表失败: "+err.Error())
		return
	}

	// 管理员显示所有角色，非管理员过滤管理员角色
	id, _ := strconv.ParseInt(userId, 10, 64)
	if !model.IsAdminUser(id) {
		filteredRoles := make([]*model.SysRole, 0, len(roles))
		for _, role := range roles {
			if !role.IsAdmin() {
				filteredRoles = append(filteredRoles, role)
			}
		}
		response.Put("roles", filteredRoles)
	} else {
		response.Put("roles", roles)
	}

	// 查询所有岗位
	posts, err := c.postService.SelectPostAll()
	if err != nil {
		c.ErrorJSON(ctx, "查询岗位列表失败: "+err.Error())
		return
	}
	response.Put("posts", posts)

	c.JSON(ctx, common.Success, response)
}

// Add 新增用户
// @Router /system/user [post]
func (c *SysUserController) Add(ctx *gin.Context) {
	var user model.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验部门数据权限
	c.deptService.CheckDeptDataScope(user.DeptID)

	// 校验角色数据权限
	c.roleService.CheckRoleDataScopeByIds(user.RoleIDs)

	// 校验用户名唯一性
	if !c.userService.CheckUserNameUnique(&user) {
		c.ErrorJSON(ctx, "新增用户'"+user.UserName+"'失败，登录账号已存在")
		return
	}

	// 校验手机号码唯一性
	if user.PhoneNumber != "" && !c.userService.CheckPhoneUnique(&user) {
		c.ErrorJSON(ctx, "新增用户'"+user.UserName+"'失败，手机号码已存在")
		return
	}

	// 校验邮箱唯一性
	if user.Email != "" && !c.userService.CheckEmailUnique(&user) {
		c.ErrorJSON(ctx, "新增用户'"+user.UserName+"'失败，邮箱账号已存在")
		return
	}

	// 设置创建者
	user.CreateBy = c.GetUsername(ctx)

	// 加密密码
	user.Password = utils.EncryptPassword(user.Password)

	// 新增用户
	userId, err := c.userService.InsertUser(&user)
	if err != nil {
		c.ErrorJSON(ctx, "新增用户失败: "+err.Error())
		return
	}

	c.JSON(ctx, common.Success, c.ToAjax(int(userId)))
}

// Edit 修改用户
// @Router /system/user [put]
func (c *SysUserController) Edit(ctx *gin.Context) {
	var user model.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验用户是否允许操作
	c.userService.CheckUserAllowed(&user)

	// 校验用户数据权限
	c.userService.CheckUserDataScope(user.UserID)

	// 校验部门数据权限
	c.deptService.CheckDeptDataScope(user.DeptID)

	// 校验角色数据权限
	c.roleService.CheckRoleDataScopeByIds(user.RoleIDs)

	// 校验用户名唯一性
	if !c.userService.CheckUserNameUnique(&user) {
		c.ErrorJSON(ctx, "修改用户'"+user.UserName+"'失败，登录账号已存在")
		return
	}

	// 校验手机号码唯一性
	if user.PhoneNumber != "" && !c.userService.CheckPhoneUnique(&user) {
		c.ErrorJSON(ctx, "修改用户'"+user.UserName+"'失败，手机号码已存在")
		return
	}

	// 校验邮箱唯一性
	if user.Email != "" && !c.userService.CheckEmailUnique(&user) {
		c.ErrorJSON(ctx, "修改用户'"+user.UserName+"'失败，邮箱账号已存在")
		return
	}

	// 设置更新者
	user.UpdateBy = c.GetUsername(ctx)

	// 修改用户
	result := c.userService.UpdateUser(&user)

	c.JSON(ctx, common.Success, c.ToAjax(result))
}

// Remove 删除用户
// @Router /system/user/{userIds} [delete]
func (c *SysUserController) Remove(ctx *gin.Context) {
	userIdsStr := ctx.Param("userIds")
	userIdsArr := strings.Split(userIdsStr, ",")
	userIds := make([]int64, 0, len(userIdsArr))

	// 转换用户ID
	for _, idStr := range userIdsArr {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		userIds = append(userIds, id)
	}

	// 检查当前用户是否在删除的用户中
	currentUserId := c.GetUserId(ctx)
	for _, id := range userIds {
		if id == currentUserId {
			c.ErrorJSON(ctx, "当前用户不能删除")
			return
		}
	}

	// 删除用户
	result := c.userService.DeleteUserByIds(userIds)

	c.JSON(ctx, common.Success, c.ToAjax(result))
}

// ResetPwd 重置密码
// @Router /system/user/resetPwd [put]
func (c *SysUserController) ResetPwd(ctx *gin.Context) {
	var user model.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验用户是否允许操作
	c.userService.CheckUserAllowed(&user)

	// 校验用户数据权限
	c.userService.CheckUserDataScope(user.UserID)

	// 加密密码
	user.Password = utils.EncryptPassword(user.Password)

	// 设置更新者
	user.UpdateBy = c.GetUsername(ctx)

	// 重置密码
	result := c.userService.ResetPwd(&user)

	c.JSON(ctx, common.Success, c.ToAjax(result))
}

// ChangeStatus 状态修改
// @Router /system/user/changeStatus [put]
func (c *SysUserController) ChangeStatus(ctx *gin.Context) {
	var user model.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验用户是否允许操作
	c.userService.CheckUserAllowed(&user)

	// 校验用户数据权限
	c.userService.CheckUserDataScope(user.UserID)

	// 设置更新者
	user.UpdateBy = c.GetUsername(ctx)

	// 修改用户状态
	result := c.userService.UpdateUserStatus(&user)

	c.JSON(ctx, common.Success, c.ToAjax(result))
}

// AuthRole 根据用户编号获取授权角色
// @Router /system/user/authRole/{userId} [get]
func (c *SysUserController) AuthRole(ctx *gin.Context) {
	userIdStr := ctx.Param("userId")
	userId, _ := strconv.ParseInt(userIdStr, 10, 64)

	response := common.SuccessResponse()

	// 查询用户信息
	user, err := c.userService.SelectUserById(userId)
	if err != nil {
		c.ErrorJSON(ctx, "查询用户信息失败: "+err.Error())
		return
	}

	// 查询用户角色
	roles, err := c.roleService.SelectRolesByUserId(userId)
	if err != nil {
		c.ErrorJSON(ctx, "查询用户角色失败: "+err.Error())
		return
	}

	response.Put("user", user)

	// 管理员显示所有角色，非管理员过滤管理员角色
	if model.IsAdminUser(userId) {
		response.Put("roles", roles)
	} else {
		filteredRoles := make([]*model.SysRole, 0, len(roles))
		for _, role := range roles {
			if !role.IsAdmin() {
				filteredRoles = append(filteredRoles, role)
			}
		}
		response.Put("roles", filteredRoles)
	}

	c.JSON(ctx, common.Success, response)
}

// InsertAuthRole 用户授权角色
// @Router /system/user/authRole [put]
func (c *SysUserController) InsertAuthRole(ctx *gin.Context) {
	userIdStr := ctx.Query("userId")
	roleIdsStr := ctx.Query("roleIds")

	userId, _ := strconv.ParseInt(userIdStr, 10, 64)
	roleIdsArr := strings.Split(roleIdsStr, ",")
	roleIds := make([]int64, 0, len(roleIdsArr))

	// 转换角色ID
	for _, idStr := range roleIdsArr {
		if idStr == "" {
			continue
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		roleIds = append(roleIds, id)
	}

	// 校验用户数据权限
	c.userService.CheckUserDataScope(userId)

	// 校验角色数据权限
	c.roleService.CheckRoleDataScopeByIds(roleIds)

	// 保存用户角色
	err := c.userService.InsertUserAuth(userId, roleIds)
	if err != nil {
		c.ErrorJSON(ctx, "保存用户角色失败: "+err.Error())
		return
	}

	c.JSON(ctx, common.Success, c.Success())
}
