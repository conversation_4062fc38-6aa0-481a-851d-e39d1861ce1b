package util

import (
	"backend/internal/job/constants"
	"backend/internal/model"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"time"
)

// JobExecutor 任务执行器
type JobExecutor struct {
	// 任务对象
	Job *model.SysJob
	// 任务日志
	JobLog *model.SysJobLog
}

// NewJobExecutor 创建任务执行器
func NewJobExecutor(job *model.SysJob) *JobExecutor {
	return &JobExecutor{
		Job: job,
		JobLog: &model.SysJobLog{
			JobName:      job.JobName,
			JobGroup:     job.JobGroup,
			InvokeTarget: job.InvokeTarget,
			StartTime:    &time.Time{},
		},
	}
}

// Execute 执行任务
func (e *JobExecutor) Execute() {
	// 记录开始时间
	startTime := time.Now()
	e.JobLog.StartTime = &startTime

	// 执行任务
	err := e.invokeMethod()

	// 记录结束时间
	stopTime := time.Now()
	e.JobLog.StopTime = &stopTime

	// 处理执行结果
	if err != nil {
		e.JobLog.Status = "1" // 失败
		e.JobLog.ExceptionInfo = err.Error()
	} else {
		e.JobLog.Status = "0" // 成功
	}
}

// invokeMethod 调用目标方法
func (e *JobExecutor) invokeMethod() error {
	// 解析调用目标字符串
	invokeTarget := strings.TrimSpace(e.Job.InvokeTarget)
	if invokeTarget == "" {
		return errors.New("调用目标字符串不能为空")
	}

	// 解析方法名和参数
	parts := strings.Split(invokeTarget, " ")
	if len(parts) == 0 {
		return errors.New("调用目标格式错误")
	}

	// 获取方法名
	methodName := parts[0]
	e.JobLog.JobMessage = fmt.Sprintf("执行方法: %s", methodName)

	// 这里需要根据实际情况实现方法调用
	// 为了简化，我们使用反射来调用已注册的方法
	method := reflect.ValueOf(JobTasks).MethodByName(methodName)
	if !method.IsValid() {
		return fmt.Errorf("找不到方法: %s", methodName)
	}

	// 准备参数
	var args []reflect.Value
	if len(parts) > 1 {
		for _, arg := range parts[1:] {
			args = append(args, reflect.ValueOf(arg))
		}
	}

	// 调用方法
	results := method.Call(args)

	// 处理返回结果
	if len(results) > 0 && !results[0].IsNil() {
		if err, ok := results[0].Interface().(error); ok {
			return err
		}
	}

	return nil
}

// IsConcurrent 是否允许并发执行
func (e *JobExecutor) IsConcurrent() bool {
	return e.Job.Concurrent == constants.CONCURRENT_ALLOWED
}

// JobTasks 任务集合，所有可执行的任务都需要在这里注册
var JobTasks = &jobTasks{}

// jobTasks 任务集合实现
type jobTasks struct{}

// NoParams 无参数示例任务
func (t *jobTasks) NoParams() error {
	fmt.Println("执行无参数任务")
	return nil
}

// HasParams 有参数示例任务
func (t *jobTasks) HasParams(param1 string, param2 string) error {
	fmt.Printf("执行有参数任务: %s, %s\n", param1, param2)
	return nil
}

// ThrowException 抛出异常示例任务
func (t *jobTasks) ThrowException() error {
	return errors.New("任务执行失败")
}
