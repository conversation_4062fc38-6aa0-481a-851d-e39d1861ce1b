package util

import (
	"backend/internal/job/constants"
	"backend/internal/model"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// JobExecutor 任务执行器
type JobExecutor struct {
	job      *model.SysJob
	jobStore JobStore
}

// JobStore 任务存储接口
type JobStore interface {
	GetJob(jobId int64) (*model.SysJob, error)
	UpdateJobStatus(job *model.SysJob) error
	AddJobLog(log *model.SysJobLog) error
}

// NewJobExecutor 创建任务执行器
func NewJobExecutor(job *model.SysJob, jobStore JobStore) *JobExecutor {
	return &JobExecutor{
		job:      job,
		jobStore: jobStore,
	}
}

// Run 执行任务
func (e *JobExecutor) Run() {
	startTime := time.Now()

	// 创建任务日志
	jobLog := &model.SysJobLog{
		JobName:      e.job.JobName,
		JobGroup:     e.job.JobGroup,
		InvokeTarget: e.job.InvokeTarget,
		JobMessage:   "任务执行开始",
		Status:       "0", // 执行中
		StartTime:    &startTime,
	}

	// 执行任务
	var err error
	defer func() {
		stopTime := time.Now()
		jobLog.StopTime = &stopTime

		if r := recover(); r != nil {
			jobLog.Status = "1" // 失败
			jobLog.ExceptionInfo = fmt.Sprintf("任务执行异常: %v", r)
			logrus.Errorf("任务执行异常: %v", r)
		} else if err != nil {
			jobLog.Status = "1" // 失败
			jobLog.ExceptionInfo = fmt.Sprintf("任务执行错误: %s", err.Error())
			logrus.Errorf("任务执行错误: %s", err.Error())
		} else {
			jobLog.Status = "0" // 成功
			jobLog.JobMessage = fmt.Sprintf("%s 总共耗时：%d毫秒", e.job.JobName, stopTime.Sub(startTime).Milliseconds())
		}

		// 记录日志
		if e.jobStore != nil {
			e.jobStore.AddJobLog(jobLog)
		}
	}()

	// 解析并执行目标方法
	err = InvokeMethod(e.job.InvokeTarget)
}

// InvokeMethod 解析并执行目标方法
func InvokeMethod(invokeTarget string) error {
	// 解析调用目标
	beanName := getBeanName(invokeTarget)
	methodName := getMethodName(invokeTarget)
	methodParams := getMethodParams(invokeTarget)

	// 在实际应用中，这里需要通过反射或依赖注入容器获取bean
	// 这里简化处理，仅打印调用信息
	logrus.Infof("执行任务: %s.%s(%v)", beanName, methodName, methodParams)

	// TODO: 实现实际的方法调用逻辑
	// 这里可以通过反射调用注册的服务方法
	// 或者通过一个任务注册表来查找并执行任务

	return nil
}

// 获取bean名称
func getBeanName(invokeTarget string) string {
	beanName := strings.Split(invokeTarget, "(")[0]
	return strings.Join(strings.Split(beanName, ".")[:len(strings.Split(beanName, "."))-1], ".")
}

// 获取方法名称
func getMethodName(invokeTarget string) string {
	methodName := strings.Split(invokeTarget, "(")[0]
	parts := strings.Split(methodName, ".")
	return parts[len(parts)-1]
}

// 获取方法参数
func getMethodParams(invokeTarget string) []interface{} {
	// 提取括号内的参数
	paramsStr := ""
	if idx := strings.Index(invokeTarget, "("); idx != -1 {
		if endIdx := strings.LastIndex(invokeTarget, ")"); endIdx != -1 {
			paramsStr = invokeTarget[idx+1 : endIdx]
		}
	}

	if paramsStr == "" {
		return nil
	}

	// 解析参数
	params := strings.Split(paramsStr, ",")
	result := make([]interface{}, 0, len(params))

	for _, param := range params {
		param = strings.TrimSpace(param)
		if param == "" {
			continue
		}

		// 字符串类型
		if strings.HasPrefix(param, "'") && strings.HasSuffix(param, "'") ||
			strings.HasPrefix(param, "\"") && strings.HasSuffix(param, "\"") {
			result = append(result, param[1:len(param)-1])
			continue
		}

		// 布尔类型
		if param == "true" || param == "false" {
			result = append(result, param == "true")
			continue
		}

		// 长整型
		if strings.HasSuffix(param, "L") {
			val := param[:len(param)-1]
			result = append(result, val) // 简化处理，实际应转换为int64
			continue
		}

		// 浮点型
		if strings.HasSuffix(param, "D") {
			val := param[:len(param)-1]
			result = append(result, val) // 简化处理，实际应转换为float64
			continue
		}

		// 默认为整型
		result = append(result, param) // 简化处理，实际应转换为int
	}

	return result
}

// ExecuteTask 执行任务
func ExecuteTask(taskName string, params ...interface{}) {
	logrus.Infof("执行任务: %s, 参数: %v", taskName, params)

	// 在实际应用中，这里应该是一个任务注册表，通过taskName查找对应的任务函数
	// 这里简化处理，仅打印任务信息
}

// RegisterTask 注册任务
func RegisterTask(taskName string, task interface{}) {
	// 检查task是否是函数
	if reflect.TypeOf(task).Kind() != reflect.Func {
		logrus.Errorf("注册任务失败: %s 不是一个函数", taskName)
		return
	}

	// 在实际应用中，这里应该将任务注册到一个任务注册表中
	logrus.Infof("注册任务: %s", taskName)
}

// IsConcurrent 是否允许并发执行
func (e *JobExecutor) IsConcurrent() bool {
	return e.job.Concurrent == constants.CONCURRENT_ALLOWED
}

// JobTasks 任务集合，所有可执行的任务都需要在这里注册
var JobTasks = &jobTasks{}

// jobTasks 任务集合实现
type jobTasks struct{}

// NoParams 无参数示例任务
func (t *jobTasks) NoParams() error {
	fmt.Println("执行无参数任务")
	return nil
}

// HasParams 有参数示例任务
func (t *jobTasks) HasParams(param1 string, param2 string) error {
	fmt.Printf("执行有参数任务: %s, %s\n", param1, param2)
	return nil
}

// ThrowException 抛出异常示例任务
func (t *jobTasks) ThrowException() error {
	return errors.New("任务执行失败")
}
