package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysRoleRepository 角色Repository接口
type SysRoleRepository interface {
	Repository
	// SelectRoleList 查询角色列表
	SelectRoleList(role *model.SysRole) ([]*model.SysRole, error)
	// SelectRoleById 根据角色ID查询角色
	SelectRoleById(roleId int64) (*model.SysRole, error)
	// SelectRolesByUserName 根据用户名查询角色
	SelectRolesByUserName(userName string) ([]*model.SysRole, error)
	// SelectRolesByUserId 根据用户ID查询角色
	SelectRolesByUserId(userId int64) ([]*model.SysRole, error)
	// InsertRole 新增角色信息
	InsertRole(role *model.SysRole) (int64, error)
	// UpdateRole 修改角色信息
	UpdateRole(role *model.SysRole) error
	// DeleteRoleById 通过角色ID删除角色
	DeleteRoleById(roleId int64) error
	// DeleteRoleByIds 批量删除角色信息
	DeleteRoleByIds(roleIds []int64) error
	// CheckRoleNameUnique 校验角色名称是否唯一
	CheckRoleNameUnique(role *model.SysRole) (bool, error)
	// CheckRoleKeyUnique 校验角色权限是否唯一
	CheckRoleKeyUnique(role *model.SysRole) (bool, error)
}

// SysRoleRepositoryImpl 角色Repository实现
type SysRoleRepositoryImpl struct {
	*BaseRepository
}

// NewSysRoleRepository 创建角色Repository
func NewSysRoleRepository(db *gorm.DB) SysRoleRepository {
	return &SysRoleRepositoryImpl{
		BaseRepository: NewBaseRepository(db),
	}
}

// SelectRoleList 查询角色列表
func (r *SysRoleRepositoryImpl) SelectRoleList(role *model.SysRole) ([]*model.SysRole, error) {
	var roles []*model.SysRole
	db := r.DB.Model(&model.SysRole{}).Where("del_flag = '0'")

	if role.RoleID != 0 {
		db = db.Where("role_id = ?", role.RoleID)
	}
	if role.RoleName != "" {
		db = db.Where("role_name LIKE ?", "%"+role.RoleName+"%")
	}
	if role.Status != "" {
		db = db.Where("status = ?", role.Status)
	}
	if role.RoleKey != "" {
		db = db.Where("role_key LIKE ?", "%"+role.RoleKey+"%")
	}

	err := db.Find(&roles).Error
	return roles, err
}

// SelectRoleById 根据角色ID查询角色
func (r *SysRoleRepositoryImpl) SelectRoleById(roleId int64) (*model.SysRole, error) {
	var role model.SysRole
	err := r.DB.Where("role_id = ? AND del_flag = '0'", roleId).First(&role).Error
	if err != nil {
		return nil, err
	}
	return &role, nil
}

// SelectRolesByUserName 根据用户名查询角色
func (r *SysRoleRepositoryImpl) SelectRolesByUserName(userName string) ([]*model.SysRole, error) {
	var roles []*model.SysRole
	err := r.DB.Model(&model.SysRole{}).
		Joins("INNER JOIN sys_user_role ur ON ur.role_id = sys_role.role_id").
		Joins("INNER JOIN sys_user u ON u.user_id = ur.user_id").
		Where("u.user_name = ? AND sys_role.del_flag = '0'", userName).
		Find(&roles).Error
	return roles, err
}

// SelectRolesByUserId 根据用户ID查询角色
func (r *SysRoleRepositoryImpl) SelectRolesByUserId(userId int64) ([]*model.SysRole, error) {
	var roles []*model.SysRole
	err := r.DB.Model(&model.SysRole{}).
		Joins("INNER JOIN sys_user_role ur ON ur.role_id = sys_role.role_id").
		Where("ur.user_id = ? AND sys_role.del_flag = '0'", userId).
		Find(&roles).Error
	return roles, err
}

// InsertRole 新增角色信息
func (r *SysRoleRepositoryImpl) InsertRole(role *model.SysRole) (int64, error) {
	err := r.DB.Create(role).Error
	if err != nil {
		return 0, err
	}
	return role.RoleID, nil
}

// UpdateRole 修改角色信息
func (r *SysRoleRepositoryImpl) UpdateRole(role *model.SysRole) error {
	return r.DB.Updates(role).Error
}

// DeleteRoleById 通过角色ID删除角色
func (r *SysRoleRepositoryImpl) DeleteRoleById(roleId int64) error {
	return r.DB.Model(&model.SysRole{}).Where("role_id = ?", roleId).Update("del_flag", "2").Error
}

// DeleteRoleByIds 批量删除角色信息
func (r *SysRoleRepositoryImpl) DeleteRoleByIds(roleIds []int64) error {
	return r.DB.Model(&model.SysRole{}).Where("role_id IN ?", roleIds).Update("del_flag", "2").Error
}

// CheckRoleNameUnique 校验角色名称是否唯一
func (r *SysRoleRepositoryImpl) CheckRoleNameUnique(role *model.SysRole) (bool, error) {
	var count int64
	db := r.DB.Model(&model.SysRole{}).Where("role_name = ?", role.RoleName)
	if role.RoleID != 0 {
		db = db.Where("role_id <> ?", role.RoleID)
	}
	err := db.Count(&count).Error
	if err != nil {
		return false, err
	}
	return count == 0, nil
}

// CheckRoleKeyUnique 校验角色权限是否唯一
func (r *SysRoleRepositoryImpl) CheckRoleKeyUnique(role *model.SysRole) (bool, error) {
	var count int64
	db := r.DB.Model(&model.SysRole{}).Where("role_key = ?", role.RoleKey)
	if role.RoleID != 0 {
		db = db.Where("role_id <> ?", role.RoleID)
	}
	err := db.Count(&count).Error
	if err != nil {
		return false, err
	}
	return count == 0, nil
}
