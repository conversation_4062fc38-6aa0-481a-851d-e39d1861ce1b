package middleware

import (
	"backend/internal/constants"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// Auth 认证中间件
func Auth(tokenService service.TokenService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求头中的token
		token := c.<PERSON>eader(constants.AUTHENTICATION)
		if token == "" {
			// 尝试从URL参数中获取
			token = c.Query(constants.TOKEN_PARAM)
		}

		if token == "" || !strings.HasPrefix(token, constants.TOKEN_PREFIX) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证失败，无效的令牌",
			})
			c.Abort()
			return
		}

		// 去除Bearer前缀
		token = strings.TrimPrefix(token, constants.TOKEN_PREFIX)

		// 验证token
		claims, err := utils.ParseToken(token)
		if err != nil {
			c.<PERSON><PERSON><PERSON>(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证失败，令牌已过期或验证不正确",
			})
			c.Abort()
			return
		}

		// 获取用户信息
		loginUser, err := tokenService.GetLoginUser(token)
		if err != nil || loginUser == nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证失败，找不到用户信息",
			})
			c.Abort()
			return
		}

		// 验证token是否过期
		if tokenService.IsTokenExpired(loginUser) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证失败，令牌已过期",
			})
			c.Abort()
			return
		}

		// 刷新token过期时间
		tokenService.VerifyToken(loginUser)

		// 将用户信息保存到上下文
		c.Set(constants.LOGIN_USER_KEY, loginUser)
		c.Set(constants.USER_ID_KEY, claims.UserId)
		c.Set(constants.USERNAME_KEY, claims.Username)

		c.Next()
	}
}

// Permission 权限验证中间件
func Permission(permission string, permissionService service.SysPermissionService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户信息
		userObj, exists := c.Get(constants.LOGIN_USER_KEY)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证失败，无法获取用户信息",
			})
			c.Abort()
			return
		}

		loginUser, ok := userObj.(*model.LoginUser)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "认证失败，用户信息类型不正确",
			})
			c.Abort()
			return
		}

		// 超级管理员拥有所有权限
		if model.IsAdminUser(loginUser.User.UserID) {
			c.Next()
			return
		}

		// 验证用户是否有权限
		if !permissionService.HasPermission(loginUser.Permissions, permission) {
			c.JSON(http.StatusForbidden, gin.H{
				"code": 403,
				"msg":  "没有权限，请联系管理员",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
