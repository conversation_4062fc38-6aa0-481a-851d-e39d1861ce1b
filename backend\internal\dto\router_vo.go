package dto

// RouterVo 路由配置信息
type RouterVo struct {
	// 路由名字
	Name string `json:"name"`

	// 路由地址
	Path string `json:"path"`

	// 是否隐藏路由，当设置 true 的时候该路由不会再侧边栏出现
	Hidden bool `json:"hidden"`

	// 重定向地址，当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
	Redirect string `json:"redirect,omitempty"`

	// 组件地址
	Component string `json:"component"`

	// 路由参数：如 {"id": 1, "name": "ry"}
	Query string `json:"query,omitempty"`

	// 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
	AlwaysShow bool `json:"alwaysShow,omitempty"`

	// 其他元素
	Meta *MetaVo `json:"meta,omitempty"`

	// 子路由
	Children []*RouterVo `json:"children,omitempty"`
}
