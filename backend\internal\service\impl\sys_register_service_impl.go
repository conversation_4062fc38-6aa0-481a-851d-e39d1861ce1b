package impl

import (
	"backend/internal/constants"
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
	"errors"
	"fmt"
)

// SysRegisterServiceImpl 注册服务实现
type SysRegisterServiceImpl struct {
	userService   service.SysUserService
	configService service.SysConfigService
}

// NewSysRegisterService 创建注册服务实例
func NewSysRegisterService(userService service.SysUserService, configService service.SysConfigService) service.SysRegisterService {
	return &SysRegisterServiceImpl{
		userService:   userService,
		configService: configService,
	}
}

// Register 用户注册
func (s *SysRegisterServiceImpl) Register(registerBody *model.RegisterBody) (string, error) {
	username := registerBody.Username
	password := registerBody.Password

	// 创建用户对象
	sysUser := &model.SysUser{
		UserName: username,
	}

	// 验证码校验 - 暂时不实现，等后续实现缓存服务后再添加
	// captchaEnabled := s.configService.SelectCaptchaEnabled()
	// if captchaEnabled {
	//     s.validateCaptcha(username, registerBody.Code, registerBody.UUID)
	// }

	// 校验用户名
	if username == "" {
		return "用户名不能为空", nil
	} else if password == "" {
		return "用户密码不能为空", nil
	} else if len(username) < constants.USERNAME_MIN_LENGTH || len(username) > constants.USERNAME_MAX_LENGTH {
		return "账户长度必须在2到20个字符之间", nil
	} else if len(password) < constants.PASSWORD_MIN_LENGTH || len(password) > constants.PASSWORD_MAX_LENGTH {
		return "密码长度必须在5到20个字符之间", nil
	} else if !s.userService.CheckUserNameUnique(sysUser) {
		return fmt.Sprintf("保存用户'%s'失败，注册账号已存在", username), nil
	}

	// 设置用户信息
	sysUser.NickName = username
	sysUser.PwdUpdateDate = utils.GetNowDate()
	sysUser.Password = utils.EncryptPassword(password)

	// 注册用户
	regFlag := s.userService.RegisterUser(sysUser)
	if !regFlag {
		return "注册失败,请联系系统管理人员", nil
	}

	// 记录注册日志 - 暂时不实现，等后续实现日志服务后再添加
	// AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.REGISTER, MessageUtils.message("user.register.success")));

	return "", nil
}

// validateCaptcha 校验验证码
func (s *SysRegisterServiceImpl) validateCaptcha(username, code, uuid string) error {
	// 暂时不实现，等后续实现缓存服务后再添加
	// verifyKey := constants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "")
	// captcha := redisCache.getCacheObject(verifyKey)
	// redisCache.deleteObject(verifyKey)
	// if captcha == nil {
	//     throw new CaptchaExpireException()
	// }
	// if !code.equalsIgnoreCase(captcha) {
	//     throw new CaptchaException()
	// }
	return errors.New("验证码校验暂未实现")
}
