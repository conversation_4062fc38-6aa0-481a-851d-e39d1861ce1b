@echo off
chcp 65001 >nul
echo ========================================
echo        若依Go后端构建脚本
echo ========================================

:: 设置变量
set APP_NAME=ruoyi-go
set VERSION=1.0.0
set BUILD_DIR=build
set BACKEND_DIR=backend

:: 检查Go环境
echo 🔍 检查Go环境...
go version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Go环境未安装或未配置到PATH
    echo 请先安装Go并配置环境变量
    pause
    exit /b 1
)
echo ✅ Go环境检查通过

:: 检查后端目录
if not exist "%BACKEND_DIR%" (
    echo ❌ 后端目录 %BACKEND_DIR% 不存在
    pause
    exit /b 1
)

:: 进入后端目录
cd %BACKEND_DIR%

:: 检查go.mod文件
if not exist "go.mod" (
    echo ❌ go.mod文件不存在，请先初始化Go模块
    pause
    exit /b 1
)

:: 创建构建目录
if not exist "%BUILD_DIR%" (
    mkdir %BUILD_DIR%
)

echo 📦 开始构建Go后端...

:: 设置Go环境变量
set CGO_ENABLED=1
set GOOS=windows
set GOARCH=amd64

:: 下载依赖
echo 📥 下载Go依赖包...
go mod tidy
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 下载依赖失败
    pause
    exit /b 1
)
echo ✅ 依赖下载完成

:: 运行测试
echo 🧪 运行单元测试...
go test ./... -v
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  测试失败，但继续构建
)

:: 构建应用
echo 🔨 编译Go应用...
go build -ldflags "-X main.version=%VERSION% -X main.buildTime=%date% %time%" -o %BUILD_DIR%/%APP_NAME%.exe .
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)
echo ✅ 构建成功

:: 复制配置文件
echo 📋 复制配置文件...
if exist "config" (
    xcopy /E /I /Y config %BUILD_DIR%\config
)

:: 复制SQL脚本
echo 📄 复制SQL脚本...
if exist "sql" (
    xcopy /E /I /Y sql %BUILD_DIR%\sql
)

:: 创建启动脚本
echo 📝 创建启动脚本...
echo @echo off > %BUILD_DIR%\start.bat
echo chcp 65001 ^>nul >> %BUILD_DIR%\start.bat
echo echo 启动若依Go后端服务... >> %BUILD_DIR%\start.bat
echo %APP_NAME%.exe >> %BUILD_DIR%\start.bat
echo pause >> %BUILD_DIR%\start.bat

:: 创建停止脚本
echo 📝 创建停止脚本...
echo @echo off > %BUILD_DIR%\stop.bat
echo chcp 65001 ^>nul >> %BUILD_DIR%\stop.bat
echo echo 停止若依Go后端服务... >> %BUILD_DIR%\stop.bat
echo taskkill /F /IM %APP_NAME%.exe >> %BUILD_DIR%\stop.bat
echo echo 服务已停止 >> %BUILD_DIR%\stop.bat
echo pause >> %BUILD_DIR%\stop.bat

:: 显示构建信息
echo.
echo ========================================
echo           构建完成信息
echo ========================================
echo 📦 应用名称: %APP_NAME%
echo 🏷️  版本号: %VERSION%
echo 📁 构建目录: %BUILD_DIR%
echo 💾 可执行文件: %BUILD_DIR%\%APP_NAME%.exe
echo 🚀 启动脚本: %BUILD_DIR%\start.bat
echo 🛑 停止脚本: %BUILD_DIR%\stop.bat
echo.

:: 询问是否立即运行
set /p RUN_NOW=是否立即运行应用? (y/n): 
if /i "%RUN_NOW%"=="y" (
    echo 🚀 启动应用...
    cd %BUILD_DIR%
    start %APP_NAME%.exe
    echo ✅ 应用已启动
) else (
    echo 💡 您可以进入 %BUILD_DIR% 目录运行 start.bat 启动应用
)

echo.
echo 🎉 构建脚本执行完成！
pause
