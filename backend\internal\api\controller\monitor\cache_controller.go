package monitor

import (
	"backend/internal/api/controller"
	"backend/internal/constants"
	"backend/internal/model"
	"backend/internal/service"
	"fmt"

	"github.com/gin-gonic/gin"
)

// CacheController 缓存监控控制器
type CacheController struct {
	controller.BaseController
	redisCache service.RedisCache
}

// NewCacheController 创建缓存监控控制器
func NewCacheController(redisCache service.RedisCache) *CacheController {
	return &CacheController{
		redisCache: redisCache,
	}
}

// GetInfo 获取缓存监控信息
// @Summary 获取缓存监控信息
// @Description 获取缓存监控信息
// @Tags 缓存监控
// @Accept json
// @Produce json
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/cache [get]
func (c *CacheController) GetInfo(ctx *gin.Context) {
	// 获取缓存信息
	info, err := c.redisCache.GetInfo()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 获取缓存命令统计
	commandStats, err := c.redisCache.GetCommandStats()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 获取缓存大小
	dbSize, err := c.redisCache.GetDBSize()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 构建结果
	result := make(map[string]interface{})
	result["info"] = info
	result["dbSize"] = dbSize
	result["commandStats"] = commandStats

	c.Success(ctx, result)
}

// GetNames 获取缓存名称列表
// @Summary 获取缓存名称列表
// @Description 获取缓存名称列表
// @Tags 缓存监控
// @Accept json
// @Produce json
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/cache/getNames [get]
func (c *CacheController) GetNames(ctx *gin.Context) {
	// 构建缓存名称列表
	caches := []*model.SysCache{
		model.NewSysCache(constants.LOGIN_TOKEN_KEY, "用户信息"),
		model.NewSysCache(constants.SYS_CONFIG_KEY, "配置信息"),
		model.NewSysCache(constants.SYS_DICT_KEY, "数据字典"),
		model.NewSysCache(constants.CAPTCHA_CODE_KEY, "验证码"),
		model.NewSysCache(constants.REPEAT_SUBMIT_KEY, "防重提交"),
		model.NewSysCache(constants.RATE_LIMIT_KEY, "限流处理"),
		model.NewSysCache(constants.PWD_ERR_CNT_KEY, "密码错误次数"),
		model.NewSysCache(constants.ONLINE_TOKEN_KEY, "在线用户"),
	}

	c.Success(ctx, caches)
}

// GetKeys 获取缓存键名列表
// @Summary 获取缓存键名列表
// @Description 获取缓存键名列表
// @Tags 缓存监控
// @Accept json
// @Produce json
// @Param cacheName path string true "缓存名称"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/cache/getKeys/{cacheName} [get]
func (c *CacheController) GetKeys(ctx *gin.Context) {
	cacheName := ctx.Param("cacheName")

	// 获取缓存键名列表
	keys, err := c.redisCache.GetKeys(cacheName + "*")
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, keys)
}

// GetValue 获取缓存内容
// @Summary 获取缓存内容
// @Description 获取缓存内容
// @Tags 缓存监控
// @Accept json
// @Produce json
// @Param cacheName path string true "缓存名称"
// @Param cacheKey path string true "缓存键名"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/cache/getValue/{cacheName}/{cacheKey} [get]
func (c *CacheController) GetValue(ctx *gin.Context) {
	cacheName := ctx.Param("cacheName")
	cacheKey := ctx.Param("cacheKey")

	// 获取缓存内容
	cacheValue, err := c.redisCache.GetCacheObject(cacheKey)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 将缓存内容转换为字符串
	var cacheValueStr string
	switch v := cacheValue.(type) {
	case string:
		cacheValueStr = v
	case []byte:
		cacheValueStr = string(v)
	default:
		cacheValueStr = fmt.Sprintf("%v", v)
	}

	// 构建缓存信息
	sysCache := model.NewSysCacheWithKeyValue(cacheName, cacheKey, cacheValueStr)

	c.Success(ctx, sysCache)
}

// ClearCacheName 清理指定名称缓存
// @Summary 清理指定名称缓存
// @Description 清理指定名称缓存
// @Tags 缓存监控
// @Accept json
// @Produce json
// @Param cacheName path string true "缓存名称"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/cache/clearCacheName/{cacheName} [delete]
func (c *CacheController) ClearCacheName(ctx *gin.Context) {
	cacheName := ctx.Param("cacheName")

	// 获取缓存键名列表
	keys, err := c.redisCache.GetKeys(cacheName + "*")
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 删除缓存
	for _, key := range keys {
		c.redisCache.DeleteObject(key)
	}

	c.Success(ctx, nil)
}

// ClearCacheKey 清理指定键名缓存
// @Summary 清理指定键名缓存
// @Description 清理指定键名缓存
// @Tags 缓存监控
// @Accept json
// @Produce json
// @Param cacheKey path string true "缓存键名"
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/cache/clearCacheKey/{cacheKey} [delete]
func (c *CacheController) ClearCacheKey(ctx *gin.Context) {
	cacheKey := ctx.Param("cacheKey")

	// 删除缓存
	err := c.redisCache.DeleteObject(cacheKey)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// ClearCacheAll 清理所有缓存
// @Summary 清理所有缓存
// @Description 清理所有缓存
// @Tags 缓存监控
// @Accept json
// @Produce json
// @Success 200 {object} controller.Response "成功"
// @Router /monitor/cache/clearCacheAll [delete]
func (c *CacheController) ClearCacheAll(ctx *gin.Context) {
	// 获取所有缓存键名
	keys, err := c.redisCache.GetKeys("*")
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// 删除所有缓存
	for _, key := range keys {
		c.redisCache.DeleteObject(key)
	}

	c.Success(ctx, nil)
}
