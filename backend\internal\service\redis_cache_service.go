package service

import "time"

// RedisCache Redis缓存服务接口
type RedisCache interface {
	// SetCacheObject 设置缓存对象
	SetCacheObject(key string, value interface{}) error

	// SetCacheObjectWithExpiration 设置缓存对象并设置过期时间
	SetCacheObjectWithExpiration(key string, value interface{}, expiration time.Duration) error

	// GetCacheObject 获取缓存对象
	GetCacheObject(key string) (interface{}, error)

	// DeleteObject 删除缓存对象
	DeleteObject(key string) error

	// Keys 获取符合pattern的所有key
	Keys(pattern string) ([]string, error)

	// GetKeys 获取符合pattern的所有key（别名，为了兼容缓存监控）
	GetKeys(pattern string) ([]string, error)

	// GetInfo 获取缓存信息
	GetInfo() (map[string]string, error)

	// GetCommandStats 获取缓存命令统计
	GetCommandStats() ([]map[string]string, error)

	// GetDBSize 获取缓存大小
	GetDBSize() (int64, error)
}
