package model

import (
	"time"
)

// BaseModel 基础模型，对应Java中的BaseEntity
type BaseModel struct {
	// 搜索值
	SearchValue string `json:"searchValue" gorm:"-"`
	// 创建者
	CreateBy string `json:"createBy" gorm:"column:create_by;comment:创建者"`
	// 创建时间
	CreateTime *time.Time `json:"createTime" gorm:"column:create_time;comment:创建时间"`
	// 更新者
	UpdateBy string `json:"updateBy" gorm:"column:update_by;comment:更新者"`
	// 更新时间
	UpdateTime *time.Time `json:"updateTime" gorm:"column:update_time;comment:更新时间"`
	// 备注
	Remark string `json:"remark" gorm:"column:remark;comment:备注"`
	// 请求参数
	Params map[string]interface{} `json:"params" gorm:"-"`
}

// BaseEntity 为了兼容旧代码
type BaseEntity = BaseModel

// GetParams 获取请求参数，确保返回非空Map
func (b *BaseModel) GetParams() map[string]interface{} {
	if b.Params == nil {
		b.Params = make(map[string]interface{})
	}
	return b.Params
}
