package impl

import (
	"backend/internal/model"
	"backend/internal/service"
	"backend/internal/utils"
)

// SysUserOnlineServiceImpl 在线用户服务实现
type SysUserOnlineServiceImpl struct{}

// NewSysUserOnlineService 创建在线用户服务实例
func NewSysUserOnlineService() service.SysUserOnlineService {
	return &SysUserOnlineServiceImpl{}
}

// SelectOnlineByIpaddr 通过登录地址查询信息
func (s *SysUserOnlineServiceImpl) SelectOnlineByIpaddr(ipaddr string, user *model.LoginUser) *model.SysUserOnline {
	if ipaddr == user.IpAddr {
		return s.LoginUserToUserOnline(user)
	}
	return nil
}

// SelectOnlineByUserName 通过用户名称查询信息
func (s *SysUserOnlineServiceImpl) SelectOnlineByUserName(userName string, user *model.LoginUser) *model.SysUserOnline {
	if userName == utils.GetUsername(user) {
		return s.LoginUserToUserOnline(user)
	}
	return nil
}

// SelectOnlineByInfo 通过登录地址/用户名称查询信息
func (s *SysUserOnlineServiceImpl) SelectOnlineByInfo(ipaddr, userName string, user *model.LoginUser) *model.SysUserOnline {
	if ipaddr == user.IpAddr && userName == utils.GetUsername(user) {
		return s.LoginUserToUserOnline(user)
	}
	return nil
}

// LoginUserToUserOnline 设置在线用户信息
func (s *SysUserOnlineServiceImpl) LoginUserToUserOnline(user *model.LoginUser) *model.SysUserOnline {
	if user == nil || user.User == nil {
		return nil
	}

	sysUserOnline := &model.SysUserOnline{
		TokenId:       user.Token,
		UserName:      user.User.UserName,
		Ipaddr:        user.IpAddr,
		LoginLocation: user.LoginLocation,
		Browser:       user.Browser,
		OS:            user.OS,
		LoginTime:     user.LoginTime.Unix(),
	}

	// 设置部门名称
	if user.User.Dept != nil {
		sysUserOnline.DeptName = user.User.Dept.DeptName
	}

	return sysUserOnline
}
