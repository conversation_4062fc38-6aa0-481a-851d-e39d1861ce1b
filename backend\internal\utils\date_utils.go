package utils

import (
	"fmt"
	"time"
)

// GetNowDate 获取当前日期时间
func GetNowDate() *time.Time {
	now := time.Now()
	return &now
}

// FormatDate 格式化日期
func FormatDate(t *time.Time, format string) string {
	if t == nil {
		return ""
	}

	// Go的时间格式化比较特殊，使用参考时间进行格式化
	// 参考时间是: 2006-01-02 15:04:05
	switch format {
	case "yyyy-MM-dd":
		return t.Format("2006-01-02")
	case "yyyy-MM-dd HH:mm:ss":
		return t.Format("2006-01-02 15:04:05")
	case "yyyy年MM月dd日":
		return t.Format("2006年01月02日")
	case "yyyy/MM/dd":
		return t.Format("2006/01/02")
	case "yyyy/MM/dd HH:mm:ss":
		return t.Format("2006/01/02 15:04:05")
	default:
		return t.Format("2006-01-02 15:04:05")
	}
}

// ParseDate 解析日期字符串
func ParseDate(dateStr, format string) (*time.Time, error) {
	var layout string

	// 根据格式选择对应的Go时间布局
	switch format {
	case "yyyy-MM-dd":
		layout = "2006-01-02"
	case "yyyy-MM-dd HH:mm:ss":
		layout = "2006-01-02 15:04:05"
	case "yyyy年MM月dd日":
		layout = "2006年01月02日"
	case "yyyy/MM/dd":
		layout = "2006/01/02"
	case "yyyy/MM/dd HH:mm:ss":
		layout = "2006/01/02 15:04:05"
	default:
		layout = "2006-01-02 15:04:05"
	}

	t, err := time.Parse(layout, dateStr)
	if err != nil {
		return nil, err
	}
	return &t, nil
}

// AddDays 日期加上指定天数
func AddDays(t *time.Time, days int) *time.Time {
	if t == nil {
		return nil
	}

	newTime := t.AddDate(0, 0, days)
	return &newTime
}

// DaysBetween 计算两个日期之间的天数
func DaysBetween(t1, t2 *time.Time) int {
	if t1 == nil || t2 == nil {
		return 0
	}

	// 将时间部分设置为0，只比较日期部分
	date1 := time.Date(t1.Year(), t1.Month(), t1.Day(), 0, 0, 0, 0, t1.Location())
	date2 := time.Date(t2.Year(), t2.Month(), t2.Day(), 0, 0, 0, 0, t2.Location())

	// 计算差异的小时数并转换为天数
	diff := date2.Sub(date1).Hours() / 24
	return int(diff)
}

// FormatTime 格式化时间（小时、分钟、秒）
func FormatTime(hours, minutes, seconds int) string {
	return fmt.Sprintf("%02d:%02d:%02d", hours, minutes, seconds)
}

// FormatDuration 格式化时间（天、小时、分钟、秒）
func FormatDuration(days, hours, minutes, seconds int) string {
	return fmt.Sprintf("%d天%d小时%d分钟%d秒", days, hours, minutes, seconds)
}

// GetServerStartDate 获取服务器启动时间
var ServerStartTime = time.Now()
