package constants

// 任务调度通用常量
const (
	// 任务状态
	JOB_STATUS_NORMAL = "0" // 正常
	JOB_STATUS_PAUSE  = "1" // 暂停

	// 任务并发执行策略
	CONCURRENT_ALLOWED     = "0" // 允许并发执行
	CONCURRENT_NOT_ALLOWED = "1" // 禁止并发执行

	// 任务错过执行策略
	MISFIRE_DEFAULT          = "0" // 默认策略
	MISFIRE_IGNORE_MISFIRES  = "1" // 忽略错过执行
	MISFIRE_FIRE_AND_PROCEED = "2" // 立即执行一次
	MISFIRE_DO_NOTHING       = "3" // 不做任何操作
)

// 任务执行结果
const (
	JOB_SUCCESS = "0" // 成功
	JOB_FAILURE = "1" // 失败
)

// 任务组名
const (
	DEFAULT_JOB_GROUP = "DEFAULT" // 默认任务组
	SYSTEM_JOB_GROUP  = "SYSTEM"  // 系统任务组
)

// 任务调度参数键
const (
	TASK_PROPERTIES = "TASK_PROPERTIES" // 任务参数
)

// 任务类名前缀
const (
	TASK_CLASS_NAME = "TASK_CLASS_" // 任务类名前缀
)
