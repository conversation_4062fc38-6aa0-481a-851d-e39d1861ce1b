package repository

import (
	"backend/internal/model"
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"
)

// InitSystemData 初始化系统数据
func InitSystemData(db *gorm.DB) error {
	log.Println("开始初始化系统数据...")

	// 初始化系统配置
	if err := initSysConfig(db); err != nil {
		return err
	}

	// 初始化管理员用户
	if err := initAdminUser(db); err != nil {
		return err
	}

	log.Println("系统数据初始化完成")
	return nil
}

// initSysConfig 初始化系统配置
func initSysConfig(db *gorm.DB) error {
	log.Println("检查系统配置...")

	configs := []model.SysConfig{
		{
			ConfigName:  "验证码开关",
			ConfigKey:   "sys.account.captchaEnabled",
			ConfigValue: "true",
			ConfigType:  "Y",
			Remark:      "是否开启验证码功能（true开启，false关闭）",
		},
		{
			ConfigName:  "账号自助-是否开启用户注册功能",
			ConfigKey:   "sys.account.registerUser",
			ConfigValue: "true",
			ConfigType:  "Y",
			Remark:      "是否开启注册用户功能（true开启，false关闭）",
		},
		{
			ConfigName:  "用户密码字符长度",
			ConfigKey:   "sys.account.passwordLength",
			ConfigValue: "6",
			ConfigType:  "Y",
			Remark:      "密码长度最小值",
		},
		{
			ConfigName:  "用户管理-初始密码修改",
			ConfigKey:   "sys.account.initPasswordModify",
			ConfigValue: "0",
			ConfigType:  "Y",
			Remark:      "0不修改，1需要修改",
		},
		{
			ConfigName:  "主框架页-默认皮肤样式名称",
			ConfigKey:   "sys.index.skinName",
			ConfigValue: "skin-blue",
			ConfigType:  "Y",
			Remark:      "蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow",
		},
		{
			ConfigName:  "用户管理-账号密码更新周期",
			ConfigKey:   "sys.account.passwordValidateDays",
			ConfigValue: "0",
			ConfigType:  "Y",
			Remark:      "密码更新周期（0不限制，>0天数）",
		},
		{
			ConfigName:  "登录页面-默认背景图片",
			ConfigKey:   "sys.index.sideTheme",
			ConfigValue: "theme-dark",
			ConfigType:  "Y",
			Remark:      "深黑主题theme-dark，浅色主题theme-light",
		},
	}

	// 逐一添加配置，如果不存在
	for _, config := range configs {
		var count int64
		db.Model(&model.SysConfig{}).Where("config_key = ?", config.ConfigKey).Count(&count)
		if count == 0 {
			if err := db.Create(&config).Error; err != nil {
				fmt.Printf("添加系统配置 [%s] 失败: %v\n", config.ConfigKey, err)
				continue
			}
			fmt.Printf("添加系统配置 [%s] 成功\n", config.ConfigKey)
		} else {
			fmt.Printf("系统配置 [%s] 已存在\n", config.ConfigKey)
		}
	}

	return nil
}

// initAdminUser 初始化管理员用户
func initAdminUser(db *gorm.DB) error {
	log.Println("检查管理员用户...")

	var count int64
	db.Model(&model.SysUser{}).Where("user_name = ?", "admin").Count(&count)
	if count > 0 {
		fmt.Println("管理员用户已存在")
		return nil
	}

	now := time.Now()
	nowPtr := &now

	// 创建admin部门
	deptID := int64(100)
	adminDept := model.SysDept{
		DeptID:    deptID,
		ParentID:  0,
		Ancestors: "0",
		DeptName:  "总公司",
		OrderNum:  0,
		Leader:    "管理员",
		Phone:     "",
		Email:     "",
		Status:    "0",
		DelFlag:   "0",
		BaseModel: model.BaseModel{
			CreateBy:   "admin",
			CreateTime: nowPtr,
			UpdateBy:   "admin",
			UpdateTime: nowPtr,
			Remark:     "管理员部门",
		},
	}

	if err := db.Create(&adminDept).Error; err != nil {
		fmt.Printf("添加管理员部门失败: %v\n", err)
		// 检查是否是因为部门已存在
		var existingDept model.SysDept
		if err := db.Where("dept_id = ?", deptID).First(&existingDept).Error; err != nil {
			return fmt.Errorf("添加管理员部门失败: %w", err)
		}
	} else {
		fmt.Println("添加管理员部门成功")
	}

	// 创建admin角色
	roleID := int64(1)
	adminRole := model.SysRole{
		RoleID:    roleID,
		RoleName:  "超级管理员",
		RoleKey:   "admin",
		RoleSort:  1,
		DataScope: "1",
		Status:    "0",
		DelFlag:   "0",
		BaseModel: model.BaseModel{
			CreateBy:   "admin",
			CreateTime: nowPtr,
			UpdateBy:   "admin",
			UpdateTime: nowPtr,
			Remark:     "超级管理员",
		},
	}

	if err := db.Create(&adminRole).Error; err != nil {
		fmt.Printf("添加管理员角色失败: %v\n", err)
		// 检查是否是因为角色已存在
		var existingRole model.SysRole
		if err := db.Where("role_id = ?", roleID).First(&existingRole).Error; err != nil {
			return fmt.Errorf("添加管理员角色失败: %w", err)
		}
	} else {
		fmt.Println("添加管理员角色成功")
	}

	// 创建admin用户
	userID := int64(1)
	adminUser := model.SysUser{
		UserID:      userID,
		DeptID:      deptID,
		UserName:    "admin",
		NickName:    "管理员",
		Email:       "<EMAIL>",
		PhoneNumber: "",
		Sex:         "0",
		Avatar:      "",
		Password:    "admin123", // 简单密码，实际应用中应该加密
		Status:      "0",
		DelFlag:     "0",
		LoginIP:     "",
		LoginDate:   nil,
		BaseModel: model.BaseModel{
			CreateBy:   "admin",
			CreateTime: nowPtr,
			UpdateBy:   "admin",
			UpdateTime: nowPtr,
			Remark:     "管理员",
		},
	}

	if err := db.Create(&adminUser).Error; err != nil {
		fmt.Printf("添加管理员用户失败: %v\n", err)
		return fmt.Errorf("添加管理员用户失败: %w", err)
	}
	fmt.Println("添加管理员用户成功")

	// 创建用户和角色关联
	userRole := model.SysUserRole{
		UserID: userID,
		RoleID: roleID,
	}

	if err := db.Create(&userRole).Error; err != nil {
		fmt.Printf("添加用户角色关联失败: %v\n", err)
		return fmt.Errorf("添加用户角色关联失败: %w", err)
	}
	fmt.Println("添加用户角色关联成功")

	return nil
}
