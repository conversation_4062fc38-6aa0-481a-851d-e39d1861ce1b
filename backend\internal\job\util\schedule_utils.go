package util

import (
	"backend/internal/common/constants"
	"backend/internal/model"
	"errors"
	"fmt"

	"github.com/robfig/cron/v3"
)

// JobScheduler 任务调度器
type JobScheduler struct {
	cron       *cron.Cron
	jobStore   JobStoreExt
	jobEntries map[int64]cron.EntryID
}

// JobStoreExt 扩展的任务存储接口
type JobStoreExt interface {
	JobStore
	GetAllJobs() ([]*model.SysJob, error)
}

// NewJobScheduler 创建任务调度器
func NewJobScheduler(jobStore JobStoreExt) *JobScheduler {
	cronOptions := []cron.Option{
		cron.WithSeconds(), // 支持秒级调度
		cron.WithChain(cron.Recover(cron.DefaultLogger)), // 添加恢复机制
	}

	return &JobScheduler{
		cron:       cron.New(cronOptions...),
		jobStore:   jobStore,
		jobEntries: make(map[int64]cron.EntryID),
	}
}

// Start 启动调度器
func (s *JobScheduler) Start() {
	s.cron.Start()
}

// Stop 停止调度器
func (s *JobScheduler) Stop() {
	s.cron.Stop()
}

// CreateJob 创建定时任务
func (s *JobScheduler) CreateJob(job *model.SysJob) error {
	// 检查Cron表达式是否有效
	if !IsValidCronExpression(job.CronExpression) {
		return errors.New("无效的Cron表达式")
	}

	// 如果任务已存在，先删除
	if _, exists := s.jobEntries[job.JobID]; exists {
		s.DeleteJob(job)
	}

	// 创建执行器
	executor := NewJobExecutor(job, s.jobStore)

	// 根据是否允许并发执行，选择不同的执行方式
	var entryID cron.EntryID
	var err error

	if job.Concurrent == constants.CONCURRENT_ALLOWED {
		// 允许并发执行
		entryID, err = s.cron.AddFunc(job.CronExpression, func() {
			executor.Run()
		})
	} else {
		// 不允许并发执行
		entryID, err = s.cron.AddFunc(job.CronExpression, func() {
			// 使用互斥锁确保同一时间只有一个任务在执行
			// 这里简化处理，实际可以使用分布式锁
			executor.Run()
		})
	}

	if err != nil {
		return fmt.Errorf("添加任务失败: %v", err)
	}

	// 记录任务ID和EntryID的映射关系
	s.jobEntries[job.JobID] = entryID

	// 如果任务状态为暂停，则暂停任务
	if job.Status == constants.JOB_STATUS_PAUSE {
		s.PauseJob(job)
	}

	return nil
}

// UpdateJob 更新定时任务
func (s *JobScheduler) UpdateJob(job *model.SysJob) error {
	// 先删除原任务
	s.DeleteJob(job)
	// 再创建新任务
	return s.CreateJob(job)
}

// DeleteJob 删除定时任务
func (s *JobScheduler) DeleteJob(job *model.SysJob) {
	if entryID, exists := s.jobEntries[job.JobID]; exists {
		s.cron.Remove(entryID)
		delete(s.jobEntries, job.JobID)
	}
}

// PauseJob 暂停任务
func (s *JobScheduler) PauseJob(job *model.SysJob) {
	if entryID, exists := s.jobEntries[job.JobID]; exists {
		s.cron.Remove(entryID)
		// 不从映射中删除，以便后续恢复
	}
}

// ResumeJob 恢复任务
func (s *JobScheduler) ResumeJob(job *model.SysJob) error {
	// 如果任务存在于映射中但不在调度器中，重新添加
	if _, exists := s.jobEntries[job.JobID]; exists {
		return s.CreateJob(job)
	}
	return nil
}

// RunOnce 立即执行一次任务
func (s *JobScheduler) RunOnce(job *model.SysJob) {
	executor := NewJobExecutor(job, s.jobStore)
	go executor.Run()
}

// CheckJobExists 检查任务是否存在
func (s *JobScheduler) CheckJobExists(jobID int64) bool {
	_, exists := s.jobEntries[jobID]
	return exists
}

// GetRunningJobs 获取正在运行的任务
func (s *JobScheduler) GetRunningJobs() []cron.Entry {
	return s.cron.Entries()
}

// InitJobs 初始化所有任务
func (s *JobScheduler) InitJobs() error {
	// 清空现有任务
	for jobID := range s.jobEntries {
		delete(s.jobEntries, jobID)
	}

	// 重新加载所有任务
	jobs, err := s.jobStore.GetAllJobs()
	if err != nil {
		return err
	}

	for _, job := range jobs {
		if err := s.CreateJob(job); err != nil {
			// 记录错误但继续处理其他任务
			fmt.Printf("初始化任务 %s 失败: %v\n", job.JobName, err)
		}
	}

	return nil
}

// GetAllJobs 获取所有任务
func (s *JobScheduler) GetAllJobs() []*model.SysJob {
	jobs, _ := s.jobStore.GetAllJobs()
	return jobs
}

// 扩展JobStore接口
type JobStore interface {
	GetJob(jobId int64) (*model.SysJob, error)
	UpdateJobStatus(job *model.SysJob) error
	AddJobLog(log *model.SysJobLog) error
}
