package utils

import (
	"time"

	"github.com/robfig/cron/v3"
)

// GetNextExecution 获取下一次执行时间
func GetNextExecution(cronExpression string) time.Time {
	schedule, err := cron.ParseStandard(cronExpression)
	if err != nil {
		return time.Now()
	}
	return schedule.Next(time.Now())
}

// IsValidCronExpression 检查cron表达式是否有效
func IsValidCronExpression(cronExpression string) bool {
	_, err := cron.ParseStandard(cronExpression)
	return err == nil
}

// GetJobNextValidTime 获取任务的下一次有效执行时间
func GetJobNextValidTime(cronExpression string) *time.Time {
	if cronExpression != "" {
		next := GetNextExecution(cronExpression)
		return &next
	}
	return nil
}
