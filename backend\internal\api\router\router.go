package router

import (
	"backend/internal/api/controller/common"
	"backend/internal/api/controller/monitor"
	"backend/internal/api/controller/system"
	"backend/internal/api/controller/tool"
	"backend/internal/api/middleware"
	"backend/internal/job/router"
	"backend/internal/repository"
	"backend/internal/service"
	"backend/internal/service/impl"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
)

// DB 全局数据库连接
var DB *gorm.DB

// InitRouter 初始化路由
func InitRouter(r *gin.Engine) {
	// 注册中间件
	r.Use(middleware.Cors())

	// 创建内存缓存实现
	redisCache := impl.NewMemoryRedisCache()

	// 初始化数据访问层
	if DB == nil {
		panic("database connection is not initialized")
	}
	sysOperLogRepo := repository.NewSysOperLogRepository(DB)

	// 初始化服务层
	sysOperLogService := impl.NewSysOperLogService(sysOperLogRepo)
	sysTokenService := impl.NewEmptyTokenService()
	sysPermissionService := &impl.EmptyPermissionService{}

	// 注册全局中间件
	r.Use(middleware.OperLog(sysOperLogService))

	// 注册基本路由
	registerBaseRouter(r)

	// 注册不需要认证的系统路由
	registerPublicSystemRouter(r)

	// 注册需要认证的系统路由
	registerAuthSystemRouter(r, sysTokenService, sysPermissionService, redisCache)

	// 注册监控路由
	registerMonitorRouter(r, sysTokenService, sysPermissionService)

	// 注册通用路由
	registerCommonRouter(r, sysTokenService)

	// 注册工具路由
	registerToolRouter(r)

	// 注册任务调度路由
	router.RegisterJobRouter(r, DB)
}

// SetDB 设置数据库连接
func SetDB(db *gorm.DB) {
	DB = db
}

// registerBaseRouter 注册基本路由
func registerBaseRouter(r *gin.Engine) {
	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
		})
	})

	// 首页控制器
	sysIndexController := system.NewSysIndexController("RuoYi-Go", "1.0.0")
	r.GET("/", sysIndexController.Index)

	// 创建内存缓存实现
	redisCache := impl.NewMemoryRedisCache()

	// 检查数据库连接
	if DB == nil {
		panic("database connection is not initialized")
	}

	// 初始化配置服务
	sysConfigRepo := repository.NewSysConfigRepository(DB)
	sysConfigService := impl.NewSysConfigService(sysConfigRepo)

	// 验证码控制器
	captchaController := common.NewCaptchaController(sysConfigService, redisCache)
	r.GET("/captchaImage", captchaController.GetCode)
}

// registerPublicSystemRouter 注册不需要认证的系统路由
func registerPublicSystemRouter(r *gin.Engine) {
	// 检查数据库连接
	if DB == nil {
		panic("database connection is not initialized")
	}

	// 为需要sqlx接口的组件创建sqlx适配器
	sqlxDB := repository.GormToSqlx(DB)

	// 初始化数据访问层
	sysUserRepo := repository.NewSysUserRepository(DB)
	sysRoleRepo := repository.NewSysRoleRepository(DB)
	sysMenuRepo := repository.NewSysMenuRepository(sqlxDB)
	sysDeptRepo := repository.NewSysDeptRepository(DB)
	sysPostRepo := repository.NewSysPostRepository(DB)
	sysConfigRepo := repository.NewSysConfigRepository(DB)

	// 创建内存缓存实现
	redisCache := impl.NewMemoryRedisCache()

	// 创建简易的服务实现
	sysPermissionService := &impl.EmptyPermissionService{}
	sysTokenService := impl.NewEmptyTokenService()

	// 初始化服务层
	sysUserService := impl.NewSysUserService(sysUserRepo, sysRoleRepo, sysPostRepo, nil, nil, sysConfigRepo, sysDeptRepo)
	sysConfigService := impl.NewSysConfigService(sysConfigRepo)
	sysMenuService := impl.NewSysMenuService(sysMenuRepo, sysRoleRepo, nil)

	// 创建登录服务
	sysLoginService := impl.NewSysLoginService(
		sysConfigService,
		sysUserService,
		sysTokenService,
		redisCache,
		sysPermissionService,
	)

	// 系统登录控制器
	sysLoginController := system.NewSysLoginController(
		sysLoginService,
		sysMenuService,
		sysPermissionService,
		sysTokenService,
		sysConfigService,
	)

	// 注册登录相关接口
	r.POST("/login", sysLoginController.Login)
	r.GET("/getInfo", middleware.Auth(sysTokenService), sysLoginController.GetInfo)
	r.GET("/getRouters", middleware.Auth(sysTokenService), sysLoginController.GetRouters)

	// 注册服务
	sysRegisterService := impl.NewSysRegisterService(sysUserService, sysConfigService)
	sysRegisterController := system.NewSysRegisterController(sysRegisterService, sysConfigService)
	r.POST("/register", sysRegisterController.Register)
}

// registerAuthSystemRouter 注册需要认证的系统路由
func registerAuthSystemRouter(r *gin.Engine, tokenService service.TokenService, permissionService service.SysPermissionService, redisCache service.RedisCache) {
	// 检查数据库连接
	if DB == nil {
		panic("database connection is not initialized")
	}

	// 为需要sqlx接口的组件创建sqlx适配器
	sqlxDB := repository.GormToSqlx(DB)

	// 初始化数据访问层
	sysUserRepo := repository.NewSysUserRepository(DB)
	sysRoleRepo := repository.NewSysRoleRepository(DB)
	sysMenuRepo := repository.NewSysMenuRepository(sqlxDB)
	sysDeptRepo := repository.NewSysDeptRepository(DB)
	sysPostRepo := repository.NewSysPostRepository(DB)
	sysConfigRepo := repository.NewSysConfigRepository(DB)
	sysUserRoleRepo := repository.NewSysUserRoleRepository(DB)
	sysRoleMenuRepo := repository.NewSysRoleMenuRepository(DB)
	sysRoleDeptRepo := repository.NewSysRoleDeptRepository(DB)
	sysUserPostRepo := repository.NewSysUserPostRepository(DB)
	sysDictTypeRepo := repository.NewSysDictTypeRepository(DB)
	sysDictDataRepo := repository.NewSysDictDataRepository(DB)
	sysNoticeRepo := repository.NewSysNoticeRepository(DB)

	// 初始化服务层
	sysUserService := impl.NewSysUserService(sysUserRepo, sysRoleRepo, sysPostRepo, sysUserRoleRepo, sysUserPostRepo, sysConfigRepo, sysDeptRepo)
	sysRoleService := impl.NewSysRoleService(sysRoleRepo, sysRoleMenuRepo, sysRoleDeptRepo, sysUserRoleRepo)
	sysMenuService := impl.NewSysMenuService(sysMenuRepo, sysRoleRepo, sysRoleMenuRepo)
	sysDeptService := impl.NewSysDeptService(sysDeptRepo, sysRoleRepo)
	sysPostService := impl.NewSysPostService(sysPostRepo, sysUserPostRepo)
	sysConfigService := impl.NewSysConfigService(sysConfigRepo)
	sysDictTypeService := impl.NewSysDictTypeService(sysDictTypeRepo, sysDictDataRepo)
	sysDictDataService := impl.NewSysDictDataService(sysDictDataRepo)
	sysNoticeService := impl.NewSysNoticeService(sysNoticeRepo)

	// 系统控制器
	sysUserController := system.NewSysUserController(sysUserService, sysRoleService, sysDeptService, sysPostService)
	sysDeptController := system.NewSysDeptController(sysDeptService)
	sysMenuController := system.NewSysMenuController(sysMenuService)
	sysRoleController := system.NewSysRoleController(
		sysRoleService,
		sysUserService,
		sysDeptService,
		permissionService,
		tokenService,
	)
	sysPostController := system.NewSysPostController(sysPostService)
	sysConfigController := system.NewSysConfigController(sysConfigService)
	sysDictTypeController := system.NewSysDictTypeController(sysDictTypeService)
	sysDictDataController := system.NewSysDictDataController(sysDictDataService, sysDictTypeService)
	sysNoticeController := system.NewSysNoticeController(sysNoticeService)
	sysProfileController := system.NewSysProfileController(sysUserService, tokenService, "./uploads/avatar")

	// 系统模块 - 需要认证
	systemGroup := r.Group("/system")
	systemGroup.Use(middleware.Auth(tokenService))
	{
		// 防重复提交中间件
		repeatSubmitMiddleware := middleware.RepeatSubmit(10, redisCache)

		// 用户管理
		userGroup := systemGroup.Group("/user")
		{
			userGroup.GET("/list", middleware.Permission("system:user:list", permissionService), sysUserController.List)
			userGroup.GET("/:userId", middleware.Permission("system:user:query", permissionService), sysUserController.GetInfo)
			userGroup.POST("", middleware.Permission("system:user:add", permissionService), repeatSubmitMiddleware, sysUserController.Add)
			userGroup.PUT("", middleware.Permission("system:user:edit", permissionService), repeatSubmitMiddleware, sysUserController.Edit)
			userGroup.DELETE("/:userIds", middleware.Permission("system:user:remove", permissionService), sysUserController.Remove)
			userGroup.PUT("/resetPwd", middleware.Permission("system:user:resetPwd", permissionService), sysUserController.ResetPwd)
			userGroup.PUT("/changeStatus", middleware.Permission("system:user:edit", permissionService), sysUserController.ChangeStatus)
			userGroup.GET("/authRole/:userId", middleware.Permission("system:user:query", permissionService), sysUserController.AuthRole)
			userGroup.PUT("/authRole", middleware.Permission("system:user:edit", permissionService), sysUserController.InsertAuthRole)

			// 用户个人信息
			profileGroup := userGroup.Group("/profile")
			{
				profileGroup.GET("", sysProfileController.GetProfile)
				profileGroup.PUT("", repeatSubmitMiddleware, sysProfileController.UpdateProfile)
				profileGroup.PUT("/updatePwd", repeatSubmitMiddleware, sysProfileController.UpdatePwd)
				profileGroup.POST("/avatar", sysProfileController.UploadAvatar)
			}
		}

		// 部门管理
		deptGroup := systemGroup.Group("/dept")
		{
			deptGroup.GET("/list", middleware.Permission("system:dept:list", permissionService), sysDeptController.List)
			deptGroup.GET("/list/exclude/:deptId", middleware.Permission("system:dept:list", permissionService), sysDeptController.ExcludeChild)
			deptGroup.GET("/:deptId", middleware.Permission("system:dept:query", permissionService), sysDeptController.GetInfo)
			deptGroup.POST("", middleware.Permission("system:dept:add", permissionService), repeatSubmitMiddleware, sysDeptController.Add)
			deptGroup.PUT("", middleware.Permission("system:dept:edit", permissionService), repeatSubmitMiddleware, sysDeptController.Edit)
			deptGroup.DELETE("/:deptId", middleware.Permission("system:dept:remove", permissionService), sysDeptController.Remove)
		}

		// 菜单管理
		menuGroup := systemGroup.Group("/menu")
		{
			menuGroup.GET("/list", middleware.Permission("system:menu:list", permissionService), sysMenuController.List)
			menuGroup.GET("/:menuId", middleware.Permission("system:menu:query", permissionService), sysMenuController.GetInfo)
			menuGroup.GET("/treeselect", sysMenuController.TreeSelect)
			menuGroup.GET("/roleMenuTreeselect/:roleId", sysMenuController.RoleMenuTreeSelect)
			menuGroup.POST("", middleware.Permission("system:menu:add", permissionService), repeatSubmitMiddleware, sysMenuController.Add)
			menuGroup.PUT("", middleware.Permission("system:menu:edit", permissionService), repeatSubmitMiddleware, sysMenuController.Edit)
			menuGroup.DELETE("/:menuId", middleware.Permission("system:menu:remove", permissionService), sysMenuController.Remove)
		}

		// 角色管理
		roleGroup := systemGroup.Group("/role")
		{
			roleGroup.GET("/list", middleware.Permission("system:role:list", permissionService), sysRoleController.List)
			roleGroup.GET("/:roleId", middleware.Permission("system:role:query", permissionService), sysRoleController.GetInfo)
			roleGroup.POST("", middleware.Permission("system:role:add", permissionService), repeatSubmitMiddleware, sysRoleController.Add)
			roleGroup.PUT("", middleware.Permission("system:role:edit", permissionService), repeatSubmitMiddleware, sysRoleController.Edit)
			roleGroup.DELETE("/:roleIds", middleware.Permission("system:role:remove", permissionService), sysRoleController.Remove)
			roleGroup.PUT("/dataScope", middleware.Permission("system:role:edit", permissionService), repeatSubmitMiddleware, sysRoleController.DataScope)
			roleGroup.PUT("/changeStatus", middleware.Permission("system:role:edit", permissionService), sysRoleController.ChangeStatus)
			roleGroup.GET("/deptTree/:roleId", sysRoleController.DeptTree)
		}

		// 岗位管理
		postGroup := systemGroup.Group("/post")
		{
			postGroup.GET("/list", middleware.Permission("system:post:list", permissionService), sysPostController.List)
			postGroup.GET("/:postId", middleware.Permission("system:post:query", permissionService), sysPostController.GetInfo)
			postGroup.POST("", middleware.Permission("system:post:add", permissionService), repeatSubmitMiddleware, sysPostController.Add)
			postGroup.PUT("", middleware.Permission("system:post:edit", permissionService), repeatSubmitMiddleware, sysPostController.Edit)
			postGroup.DELETE("/:postIds", middleware.Permission("system:post:remove", permissionService), sysPostController.Remove)
		}

		// 字典类型
		dictTypeGroup := systemGroup.Group("/dict/type")
		{
			dictTypeGroup.GET("/list", middleware.Permission("system:dict:list", permissionService), sysDictTypeController.List)
			dictTypeGroup.GET("/:dictId", middleware.Permission("system:dict:query", permissionService), sysDictTypeController.GetInfo)
			dictTypeGroup.POST("", middleware.Permission("system:dict:add", permissionService), repeatSubmitMiddleware, sysDictTypeController.Add)
			dictTypeGroup.PUT("", middleware.Permission("system:dict:edit", permissionService), repeatSubmitMiddleware, sysDictTypeController.Edit)
			dictTypeGroup.DELETE("/:dictIds", middleware.Permission("system:dict:remove", permissionService), sysDictTypeController.Remove)
			dictTypeGroup.GET("/optionselect", sysDictTypeController.OptionSelect)
		}

		// 字典数据
		dictDataGroup := systemGroup.Group("/dict/data")
		{
			dictDataGroup.GET("/list", middleware.Permission("system:dict:list", permissionService), sysDictDataController.List)
			dictDataGroup.GET("/type/:dictType", sysDictDataController.DictType)
			dictDataGroup.GET("/:dictCode", middleware.Permission("system:dict:query", permissionService), sysDictDataController.GetInfo)
			dictDataGroup.POST("", middleware.Permission("system:dict:add", permissionService), repeatSubmitMiddleware, sysDictDataController.Add)
			dictDataGroup.PUT("", middleware.Permission("system:dict:edit", permissionService), repeatSubmitMiddleware, sysDictDataController.Edit)
			dictDataGroup.DELETE("/:dictCodes", middleware.Permission("system:dict:remove", permissionService), sysDictDataController.Remove)
		}

		// 参数设置
		configGroup := systemGroup.Group("/config")
		{
			configGroup.GET("/list", middleware.Permission("system:config:list", permissionService), sysConfigController.List)
			configGroup.GET("/:configId", middleware.Permission("system:config:query", permissionService), sysConfigController.GetInfo)
			configGroup.GET("/configKey/:configKey", sysConfigController.GetConfigKey)
			configGroup.POST("", middleware.Permission("system:config:add", permissionService), repeatSubmitMiddleware, sysConfigController.Add)
			configGroup.PUT("", middleware.Permission("system:config:edit", permissionService), repeatSubmitMiddleware, sysConfigController.Edit)
			configGroup.DELETE("/:configIds", middleware.Permission("system:config:remove", permissionService), sysConfigController.Remove)
		}

		// 通知公告
		noticeGroup := systemGroup.Group("/notice")
		{
			noticeGroup.GET("/list", middleware.Permission("system:notice:list", permissionService), sysNoticeController.List)
			noticeGroup.GET("/:noticeId", middleware.Permission("system:notice:query", permissionService), sysNoticeController.GetInfo)
			noticeGroup.POST("", middleware.Permission("system:notice:add", permissionService), repeatSubmitMiddleware, sysNoticeController.Add)
			noticeGroup.PUT("", middleware.Permission("system:notice:edit", permissionService), repeatSubmitMiddleware, sysNoticeController.Edit)
			noticeGroup.DELETE("/:noticeIds", middleware.Permission("system:notice:remove", permissionService), sysNoticeController.Remove)
		}
	}
}

// registerMonitorRouter 注册监控路由
func registerMonitorRouter(r *gin.Engine, tokenService service.TokenService, permissionService service.SysPermissionService) {
	// 检查数据库连接
	if DB == nil {
		panic("database connection is not initialized")
	}

	// 初始化缓存
	redisCache := impl.NewMemoryRedisCache()

	// 初始化服务层
	sysUserOnlineService := impl.NewSysUserOnlineService()
	sysLogininforService := impl.NewSysLogininforService(repository.NewSysLogininforRepository(DB))
	sysOperLogService := impl.NewSysOperLogService(repository.NewSysOperLogRepository(DB))
	sysPasswordService := impl.NewSysPasswordService(redisCache)

	// 创建监控控制器
	sysUserOnlineController := monitor.NewSysUserOnlineController(sysUserOnlineService, redisCache)
	sysLogininforController := monitor.NewSysLogininforController(sysLogininforService, sysPasswordService)
	sysOperlogController := monitor.NewSysOperlogController(sysOperLogService)
	cacheController := monitor.NewCacheController(redisCache)
	serverController := monitor.NewServerController()

	// 监控模块 - 需要认证
	monitorGroup := r.Group("/monitor")
	monitorGroup.Use(middleware.Auth(tokenService))
	{
		// 在线用户
		onlineGroup := monitorGroup.Group("/online")
		{
			onlineGroup.GET("/list", middleware.Permission("monitor:online:list", permissionService), sysUserOnlineController.List)
			onlineGroup.DELETE("/:tokenId", middleware.Permission("monitor:online:forceLogout", permissionService), sysUserOnlineController.ForceLogout)
		}

		// 登录日志
		logininforGroup := monitorGroup.Group("/logininfor")
		{
			logininforGroup.GET("/list", middleware.Permission("monitor:logininfor:list", permissionService), sysLogininforController.List)
			logininforGroup.DELETE("/:infoIds", middleware.Permission("monitor:logininfor:remove", permissionService), sysLogininforController.Remove)
			logininforGroup.POST("/clean", middleware.Permission("monitor:logininfor:remove", permissionService), sysLogininforController.Clean)
		}

		// 操作日志
		operlogGroup := monitorGroup.Group("/operlog")
		{
			operlogGroup.GET("/list", middleware.Permission("monitor:operlog:list", permissionService), sysOperlogController.List)
			operlogGroup.DELETE("/:operIds", middleware.Permission("monitor:operlog:remove", permissionService), sysOperlogController.Remove)
			operlogGroup.POST("/clean", middleware.Permission("monitor:operlog:remove", permissionService), sysOperlogController.Clean)
		}

		// 缓存监控
		cacheGroup := monitorGroup.Group("/cache")
		{
			cacheGroup.GET("", middleware.Permission("monitor:cache:list", permissionService), cacheController.GetInfo)
			cacheGroup.GET("/getNames", middleware.Permission("monitor:cache:list", permissionService), cacheController.GetNames)
			cacheGroup.GET("/getKeys/:cacheName", middleware.Permission("monitor:cache:list", permissionService), cacheController.GetKeys)
			cacheGroup.GET("/getValue/:cacheName/:cacheKey", middleware.Permission("monitor:cache:list", permissionService), cacheController.GetValue)
			cacheGroup.DELETE("/:cacheName/:cacheKey", middleware.Permission("monitor:cache:list", permissionService), cacheController.ClearCacheKey)
			cacheGroup.POST("/clearCache", middleware.Permission("monitor:cache:list", permissionService), cacheController.ClearCacheAll)
		}

		// 服务监控
		serverGroup := monitorGroup.Group("/server")
		{
			serverGroup.GET("", middleware.Permission("monitor:server:list", permissionService), serverController.GetInfo)
		}
	}
}

// registerCommonRouter 注册通用路由
func registerCommonRouter(r *gin.Engine, tokenService service.TokenService) {
	// 通用模块 - 需要认证
	commonGroup := r.Group("/common")
	commonGroup.Use(middleware.Auth(tokenService))
	{
		// 通用上传下载控制器
		commonController := common.NewCommonController("./uploads", "./downloads", "./resources", "http://localhost:8080")
		commonGroup.GET("/download", commonController.FileDownload)
		commonGroup.POST("/upload", commonController.UploadFile)
	}
}

// registerToolRouter 注册工具路由
func registerToolRouter(r *gin.Engine) {
	// 工具模块
	toolGroup := r.Group("/tool")
	{
		// Swagger文档
		// TODO: 实现Swagger

		// 代码生成
		// TODO: 实现代码生成

		// 系统接口
		testController := tool.NewTestController()
		testGroup := toolGroup.Group("/test")
		{
			testGroup.GET("/list", testController.UserList)
			testGroup.GET("/:id", testController.GetUser)
			testGroup.POST("", testController.Save)
			testGroup.PUT("", testController.Update)
			testGroup.DELETE("/:ids", testController.Delete)
		}
	}
}
