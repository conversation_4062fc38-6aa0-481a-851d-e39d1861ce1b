package impl

import (
	"backend/internal/constants"
	"backend/internal/service"
)

// SysPasswordServiceImpl 密码服务实现
type SysPasswordServiceImpl struct {
	redisCache service.RedisCache
}

// NewSysPasswordService 创建密码服务实例
func NewSysPasswordService(redisCache service.RedisCache) service.SysPasswordService {
	return &SysPasswordServiceImpl{
		redisCache: redisCache,
	}
}

// ClearLoginRecordCache 清除登录记录缓存
func (s *SysPasswordServiceImpl) ClearLoginRecordCache(username string) error {
	if username == "" {
		return nil
	}

	// 删除密码错误次数缓存
	return s.redisCache.DeleteObject(constants.PWD_ERR_CNT_KEY + username)
}
