package utils

import (
	"net"
	"os"
	"strings"
)

// GetHostName 获取主机名
func GetHostName() string {
	hostname, err := os.Hostname()
	if err != nil {
		return "unknown"
	}
	return hostname
}

// GetHostIP 获取主机IP
func GetHostIP() string {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "127.0.0.1"
	}

	for _, addr := range addrs {
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String()
			}
		}
	}

	return "127.0.0.1"
}

// GetRemoteIP 获取远程IP
func GetRemoteIP(ip string) string {
	if ip == "" {
		return "unknown"
	}

	// 检查是否包含多个IP，如果是，取第一个
	if strings.Contains(ip, ",") {
		return strings.TrimSpace(strings.Split(ip, ",")[0])
	}

	// 检查是否包含端口，如果是，去掉端口
	if strings.Contains(ip, ":") {
		return strings.Split(ip, ":")[0]
	}

	return ip
}
