package model

// SysDictType 字典类型表 sys_dict_type
type SysDictType struct {
	BaseModel
	// 字典主键
	DictID int64 `json:"dictId" gorm:"column:dict_id;primary_key;auto_increment;comment:字典主键"`
	// 字典名称
	DictName string `json:"dictName" gorm:"column:dict_name;comment:字典名称"`
	// 字典类型
	DictType string `json:"dictType" gorm:"column:dict_type;comment:字典类型"`
	// 状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status;default:0;comment:状态（0正常 1停用）"`
}

// TableName 设置表名
func (SysDictType) TableName() string {
	return "sys_dict_type"
}
