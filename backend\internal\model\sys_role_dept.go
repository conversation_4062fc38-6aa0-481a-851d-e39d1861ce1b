package model

// SysRoleDept 角色和部门关联 sys_role_dept
type SysRoleDept struct {
	// 角色ID
	RoleID int64 `json:"roleId" gorm:"column:role_id;not null;comment:角色ID"`
	// 部门ID
	DeptID int64 `json:"deptId" gorm:"column:dept_id;not null;comment:部门ID"`
}

// TableName 设置表名
func (SysRoleDept) TableName() string {
	return "sys_role_dept"
}

// ToString 返回结构体的字符串表示，类似Java中的toString方法
func (r *SysRoleDept) ToString() string {
	return ""
}
