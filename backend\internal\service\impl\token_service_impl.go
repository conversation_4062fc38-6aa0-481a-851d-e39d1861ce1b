package impl

import (
	"backend/internal/model"
	"backend/internal/service"
	"errors"
)

// EmptyTokenService 令牌服务空实现（用于临时替代，后续完善）
type EmptyTokenService struct{}

// CreateToken 创建令牌
func (s *EmptyTokenService) CreateToken(loginUser *model.LoginUser) (string, error) {
	return "dummy-token", nil
}

// GetLoginUser 获取登录用户
func (s *EmptyTokenService) GetLoginUser(token string) (*model.LoginUser, error) {
	if token == "dummy-token" {
		return &model.LoginUser{}, nil
	}
	return nil, errors.New("invalid token")
}

// DelLoginUser 删除登录用户
func (s *EmptyTokenService) DelLoginUser(token string) error {
	return nil
}

// RefreshToken 刷新令牌
func (s *EmptyTokenService) RefreshToken(loginUser *model.LoginUser) error {
	return nil
}

// IsTokenExpired 判断令牌是否过期
func (s *EmptyTokenService) IsTokenExpired(loginUser *model.LoginUser) bool {
	// 临时实现，始终返回未过期
	return false
}

// VerifyToken 验证令牌有效期，相差不足20分钟，自动刷新缓存
func (s *EmptyTokenService) VerifyToken(loginUser *model.LoginUser) error {
	// 临时实现，不做任何操作
	return nil
}

// NewEmptyTokenService 创建一个空的Token服务实现
func NewEmptyTokenService() service.TokenService {
	return &EmptyTokenService{}
}
