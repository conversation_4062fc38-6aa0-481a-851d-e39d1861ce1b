package repository

import (
	"backend/internal/job/util"
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysJobRepository 定时任务数据访问层
type SysJobRepository struct {
	db *gorm.DB
}

// NewSysJobRepository 创建定时任务数据访问层
func NewSysJobRepository(db *gorm.DB) *SysJobRepository {
	return &SysJobRepository{
		db: db,
	}
}

// SelectJobList 查询定时任务列表
func (r *SysJobRepository) SelectJobList(job *model.SysJob) ([]*model.SysJob, error) {
	var jobs []*model.SysJob
	query := r.db.Model(&model.SysJob{})

	if job.JobName != "" {
		query = query.Where("job_name like ?", "%"+job.JobName+"%")
	}

	if job.JobGroup != "" {
		query = query.Where("job_group = ?", job.JobGroup)
	}

	if job.Status != "" {
		query = query.Where("status = ?", job.Status)
	}

	if job.InvokeTarget != "" {
		query = query.Where("invoke_target like ?", "%"+job.InvokeTarget+"%")
	}

	err := query.Find(&jobs).Error
	return jobs, err
}

// SelectJobById 通过ID查询定时任务
func (r *SysJobRepository) SelectJobById(jobId int64) (*model.SysJob, error) {
	var job model.SysJob
	err := r.db.Where("job_id = ?", jobId).First(&job).Error
	if err != nil {
		return nil, err
	}
	return &job, nil
}

// InsertJob 新增定时任务
func (r *SysJobRepository) InsertJob(job *model.SysJob) error {
	return r.db.Create(job).Error
}

// UpdateJob 修改定时任务
func (r *SysJobRepository) UpdateJob(job *model.SysJob) error {
	return r.db.Model(&model.SysJob{}).Where("job_id = ?", job.JobID).Updates(job).Error
}

// DeleteJobById 通过ID删除定时任务
func (r *SysJobRepository) DeleteJobById(jobId int64) error {
	return r.db.Where("job_id = ?", jobId).Delete(&model.SysJob{}).Error
}

// DeleteJobByIds 批量删除定时任务
func (r *SysJobRepository) DeleteJobByIds(jobIds []int64) error {
	return r.db.Where("job_id in ?", jobIds).Delete(&model.SysJob{}).Error
}

// UpdateJobStatus 修改定时任务状态
func (r *SysJobRepository) UpdateJobStatus(job *model.SysJob) error {
	return r.db.Model(&model.SysJob{}).Where("job_id = ?", job.JobID).Update("status", job.Status).Error
}

// SelectJobAll 查询所有调度任务
func (r *SysJobRepository) SelectJobAll() ([]*model.SysJob, error) {
	var jobs []*model.SysJob
	err := r.db.Find(&jobs).Error
	return jobs, err
}

// GetJob 获取任务
func (r *SysJobRepository) GetJob(jobId int64) (*model.SysJob, error) {
	return r.SelectJobById(jobId)
}

// GetAllJobs 获取所有任务
func (r *SysJobRepository) GetAllJobs() ([]*model.SysJob, error) {
	return r.SelectJobAll()
}

// AddJobLog 添加任务日志
func (r *SysJobRepository) AddJobLog(log *model.SysJobLog) error {
	return r.db.Create(log).Error
}

// 确保SysJobRepository实现了JobStore接口
var _ util.JobStore = (*SysJobRepository)(nil)
var _ util.JobStoreExt = (*SysJobRepository)(nil)
