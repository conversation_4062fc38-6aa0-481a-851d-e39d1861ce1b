package repository

import (
	"backend/internal/model"

	"gorm.io/gorm"
)

// SysJobRepository 定时任务数据访问接口
type SysJobRepository interface {
	// SelectJobList 查询任务列表
	SelectJobList(job *model.SysJob) ([]*model.SysJob, error)

	// SelectJobById 通过ID查询任务
	SelectJobById(jobId int64) (*model.SysJob, error)

	// DeleteJob 删除任务
	DeleteJob(jobId int64) (int, error)

	// DeleteJobByIds 批量删除任务
	DeleteJobByIds(jobIds []int64) (int, error)

	// UpdateJob 修改任务
	UpdateJob(job *model.SysJob) (int, error)

	// InsertJob 新增任务
	InsertJob(job *model.SysJob) (int, error)
}

// NewSysJobRepository 创建任务仓库实例
func NewSysJobRepository(db *gorm.DB) SysJobRepository {
	return &sysJobRepository{db: db}
}

// sysJobRepository 任务仓库实现
type sysJobRepository struct {
	db *gorm.DB
}

// SelectJobList 查询任务列表
func (r *sysJobRepository) SelectJobList(job *model.SysJob) ([]*model.SysJob, error) {
	var jobs []*model.SysJob
	db := r.db.Model(&model.SysJob{})

	if job.JobName != "" {
		db = db.Where("job_name like ?", "%"+job.JobName+"%")
	}

	if job.JobGroup != "" {
		db = db.Where("job_group = ?", job.JobGroup)
	}

	if job.Status != "" {
		db = db.Where("status = ?", job.Status)
	}

	if job.InvokeTarget != "" {
		db = db.Where("invoke_target like ?", "%"+job.InvokeTarget+"%")
	}

	err := db.Find(&jobs).Error
	return jobs, err
}

// SelectJobById 通过ID查询任务
func (r *sysJobRepository) SelectJobById(jobId int64) (*model.SysJob, error) {
	var job model.SysJob
	err := r.db.Where("job_id = ?", jobId).First(&job).Error
	return &job, err
}

// DeleteJob 删除任务
func (r *sysJobRepository) DeleteJob(jobId int64) (int, error) {
	result := r.db.Delete(&model.SysJob{}, jobId)
	return int(result.RowsAffected), result.Error
}

// DeleteJobByIds 批量删除任务
func (r *sysJobRepository) DeleteJobByIds(jobIds []int64) (int, error) {
	result := r.db.Delete(&model.SysJob{}, jobIds)
	return int(result.RowsAffected), result.Error
}

// UpdateJob 修改任务
func (r *sysJobRepository) UpdateJob(job *model.SysJob) (int, error) {
	result := r.db.Save(job)
	return int(result.RowsAffected), result.Error
}

// InsertJob 新增任务
func (r *sysJobRepository) InsertJob(job *model.SysJob) (int, error) {
	result := r.db.Create(job)
	return int(result.RowsAffected), result.Error
}
