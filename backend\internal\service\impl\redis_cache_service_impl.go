package impl

import (
	"backend/internal/service"
	"errors"
	"fmt"
	"runtime"
	"strings"
	"sync"
	"time"
)

// 缓存项结构
type cacheItem struct {
	value      interface{}
	expireTime *time.Time // 过期时间，nil表示永不过期
}

// MemoryRedisCache 内存实现的Redis缓存（用于开发和测试）
type MemoryRedisCache struct {
	cache       map[string]cacheItem
	mutex       sync.RWMutex
	startTime   time.Time
	commandHits map[string]int64
}

// NewMemoryRedisCache 创建内存Redis缓存实例
func NewMemoryRedisCache() service.RedisCache {
	cache := &MemoryRedisCache{
		cache:       make(map[string]cacheItem),
		startTime:   time.Now(),
		commandHits: make(map[string]int64),
	}

	// 启动一个goroutine定期清理过期的缓存项
	go cache.cleanExpiredItems()

	return cache
}

// cleanExpiredItems 定期清理过期的缓存项
func (c *MemoryRedisCache) cleanExpiredItems() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		c.mutex.Lock()
		now := time.Now()
		for key, item := range c.cache {
			if item.expireTime != nil && now.After(*item.expireTime) {
				delete(c.cache, key)
			}
		}
		c.mutex.Unlock()
	}
}

// SetCacheObject 设置缓存对象
func (c *MemoryRedisCache) SetCacheObject(key string, value interface{}) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.cache[key] = cacheItem{value: value, expireTime: nil}
	c.commandHits["set"]++
	return nil
}

// SetCacheObjectWithExpiration 设置缓存对象并设置过期时间
func (c *MemoryRedisCache) SetCacheObjectWithExpiration(key string, value interface{}, expiration time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	expireTime := time.Now().Add(expiration)
	c.cache[key] = cacheItem{value: value, expireTime: &expireTime}
	c.commandHits["set"]++
	return nil
}

// GetCacheObject 获取缓存对象
func (c *MemoryRedisCache) GetCacheObject(key string) (interface{}, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	item, exists := c.cache[key]
	if !exists {
		return nil, errors.New("key not found")
	}

	// 检查是否过期
	if item.expireTime != nil && time.Now().After(*item.expireTime) {
		// 异步删除过期项
		go func() {
			c.mutex.Lock()
			delete(c.cache, key)
			c.mutex.Unlock()
		}()
		return nil, errors.New("key expired")
	}

	c.commandHits["get"]++
	return item.value, nil
}

// DeleteObject 删除缓存对象
func (c *MemoryRedisCache) DeleteObject(key string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	delete(c.cache, key)
	c.commandHits["del"]++
	return nil
}

// Keys 获取符合pattern的所有key
func (c *MemoryRedisCache) Keys(pattern string) ([]string, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 简单实现，仅支持"prefix*"格式的pattern
	var keys []string

	if pattern == "*" {
		// 获取所有key
		for key, item := range c.cache {
			// 跳过过期的key
			if item.expireTime != nil && time.Now().After(*item.expireTime) {
				continue
			}
			keys = append(keys, key)
		}
	} else if strings.HasSuffix(pattern, "*") {
		// 获取指定前缀的key
		prefix := pattern[:len(pattern)-1]
		for key, item := range c.cache {
			// 跳过过期的key
			if item.expireTime != nil && time.Now().After(*item.expireTime) {
				continue
			}
			if strings.HasPrefix(key, prefix) {
				keys = append(keys, key)
			}
		}
	} else {
		// 精确匹配
		if item, exists := c.cache[pattern]; exists {
			// 跳过过期的key
			if item.expireTime == nil || !time.Now().After(*item.expireTime) {
				keys = append(keys, pattern)
			}
		}
	}

	c.commandHits["keys"]++
	return keys, nil
}

// GetKeys 获取符合pattern的所有key（别名，为了兼容缓存监控）
func (c *MemoryRedisCache) GetKeys(pattern string) ([]string, error) {
	return c.Keys(pattern)
}

// GetInfo 获取缓存信息
func (c *MemoryRedisCache) GetInfo() (map[string]string, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 获取系统信息
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// 构建缓存信息
	info := make(map[string]string)
	info["redis_version"] = "memory-cache"
	info["uptime_in_seconds"] = fmt.Sprintf("%d", int(time.Since(c.startTime).Seconds()))
	info["uptime_in_days"] = fmt.Sprintf("%d", int(time.Since(c.startTime).Hours()/24))
	info["connected_clients"] = "1"
	info["used_memory"] = fmt.Sprintf("%d", memStats.Alloc)
	info["used_memory_human"] = fmt.Sprintf("%.2fM", float64(memStats.Alloc)/(1024*1024))
	info["total_system_memory"] = fmt.Sprintf("%d", memStats.Sys)
	info["total_system_memory_human"] = fmt.Sprintf("%.2fM", float64(memStats.Sys)/(1024*1024))
	info["os"] = runtime.GOOS
	info["arch_bits"] = fmt.Sprintf("%d", 8*runtime.NumCPU())
	info["cpu_cores"] = fmt.Sprintf("%d", runtime.NumCPU())
	info["process_id"] = fmt.Sprintf("%d", 0)

	return info, nil
}

// GetCommandStats 获取缓存命令统计
func (c *MemoryRedisCache) GetCommandStats() ([]map[string]string, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 构建命令统计
	var stats []map[string]string

	// 如果没有命令记录，添加一些模拟数据
	if len(c.commandHits) == 0 {
		c.commandHits["get"] = 10
		c.commandHits["set"] = 5
		c.commandHits["del"] = 2
		c.commandHits["keys"] = 3
	}

	// 遍历命令统计
	for cmd, hits := range c.commandHits {
		stat := make(map[string]string)
		stat["name"] = cmd
		stat["value"] = fmt.Sprintf("%d", hits)
		stats = append(stats, stat)
	}

	return stats, nil
}

// GetDBSize 获取缓存大小
func (c *MemoryRedisCache) GetDBSize() (int64, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 计算有效的缓存项数量（排除过期的）
	count := 0
	now := time.Now()
	for _, item := range c.cache {
		if item.expireTime == nil || !now.After(*item.expireTime) {
			count++
		}
	}

	return int64(count), nil
}
