package service

import "backend/internal/model"

// SysPostService 岗位服务接口
type SysPostService interface {
	// SelectPostList 查询岗位列表
	SelectPostList(post *model.SysPost) ([]*model.SysPost, error)

	// SelectPostById 通过岗位ID查询岗位信息
	SelectPostById(postId int64) (*model.SysPost, error)

	// SelectPostsByUserId 根据用户ID获取岗位选择框列表
	SelectPostsByUserId(userId int64) ([]*model.SysPost, error)

	// SelectPostAll 查询所有岗位
	SelectPostAll() ([]*model.SysPost, error)

	// SelectPostListByUserId 根据用户ID查询岗位ID列表
	SelectPostListByUserId(userId int64) ([]int64, error)

	// InsertPost 新增保存岗位信息
	InsertPost(post *model.SysPost) int

	// UpdatePost 修改保存岗位信息
	UpdatePost(post *model.SysPost) int

	// DeletePostByIds 批量删除岗位信息
	DeletePostByIds(postIds []int64) int

	// CheckPostNameUnique 校验岗位名称是否唯一
	CheckPostNameUnique(post *model.SysPost) bool

	// CheckPostCodeUnique 校验岗位编码是否唯一
	CheckPostCodeUnique(post *model.SysPost) bool

	// CountUserPostById 通过岗位ID查询岗位使用数量
	CountUserPostById(postId int64) (int64, error)
}
