package middleware

import (
	"backend/internal/constants"
	"backend/internal/service"
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"io"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// RepeatSubmit 防止重复提交中间件
func RepeatSubmit(interval int, redisCache service.RedisCache) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只对POST请求进行处理
		if c.Request.Method != http.MethodPost {
			c.Next()
			return
		}

		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			var err error
			requestBody, err = io.ReadAll(c.Request.Body)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "读取请求体失败"})
				c.Abort()
				return
			}
			// 重新设置请求体，以便后续中间件和控制器能够读取
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 计算请求体的哈希值
		hash := sha256.Sum256(requestBody)
		requestId := hex.EncodeToString(hash[:])

		// 生成缓存键，包含URL路径和请求体哈希
		cacheKey := constants.REPEAT_SUBMIT_KEY + ":" + c.Request.URL.Path + ":" + requestId

		// 检查是否存在缓存，存在则表示重复提交
		exists, _ := redisCache.GetCacheObject(cacheKey)
		if exists != nil {
			c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "请求正在处理中，请勿重复提交"})
			c.Abort()
			return
		}

		// 设置缓存，防止重复提交
		redisCache.SetCacheObjectWithExpiration(cacheKey, "1", time.Duration(interval)*time.Second)

		// 请求处理完成后删除缓存
		defer func() {
			// 如果请求失败，立即删除缓存
			if c.Writer.Status() != http.StatusOK {
				redisCache.DeleteObject(cacheKey)
			} else {
				// 成功时，设置较短的过期时间
				redisCache.SetCacheObjectWithExpiration(cacheKey, "1", 5*time.Second)
			}
		}()

		c.Next()
	}
}
