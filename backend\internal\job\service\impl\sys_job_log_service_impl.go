package impl

import (
	"backend/internal/job/repository"
	"backend/internal/job/service"
	"backend/internal/model"
)

// sysJobLogService 定时任务日志服务实现
type sysJobLogService struct {
	jobLogRepository repository.SysJobLogRepository
}

// NewSysJobLogService 创建任务日志服务实例
func NewSysJobLogService(jobLogRepository repository.SysJobLogRepository) service.SysJobLogService {
	return &sysJobLogService{
		jobLogRepository: jobLogRepository,
	}
}

// SelectJobLogList 获取任务日志列表
func (s *sysJobLogService) SelectJobLogList(jobLog *model.SysJobLog) ([]*model.SysJobLog, error) {
	return s.jobLogRepository.SelectJobLogList(jobLog)
}

// SelectJobLogById 根据ID获取任务日志
func (s *sysJobLogService) SelectJobLogById(jobLogId int64) (*model.SysJobLog, error) {
	return s.jobLogRepository.SelectJobLogById(jobLogId)
}

// AddJobLog 添加任务日志
func (s *sysJobLogService) AddJobLog(jobLog *model.SysJobLog) error {
	return s.jobLogRepository.InsertJobLog(jobLog)
}

// DeleteJobLogByIds 批量删除任务日志
func (s *sysJobLogService) DeleteJobLogByIds(logIds []int64) (int, error) {
	return s.jobLogRepository.DeleteJobLogByIds(logIds)
}

// DeleteJobLogById 删除任务日志
func (s *sysJobLogService) DeleteJobLogById(jobId int64) (int, error) {
	return s.jobLogRepository.DeleteJobLogById(jobId)
}

// CleanJobLog 清空任务日志
func (s *sysJobLogService) CleanJobLog() error {
	return s.jobLogRepository.CleanJobLog()
}
