package system

import (
	"backend/internal/api/common"
	"backend/internal/constants"
	"backend/internal/model"
	"backend/internal/service"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysDeptController 部门信息控制器
type SysDeptController struct {
	common.BaseController
	deptService service.SysDeptService
}

// NewSysDeptController 创建部门控制器
func NewSysDeptController(deptService service.SysDeptService) *SysDeptController {
	return &SysDeptController{
		deptService: deptService,
	}
}

// List 获取部门列表
// @Router /system/dept/list [get]
func (c *SysDeptController) List(ctx *gin.Context) {
	// 绑定查询参数
	dept := &model.SysDept{}
	if deptName := ctx.Query("deptName"); deptName != "" {
		dept.DeptName = deptName
	}
	if status := ctx.Query("status"); status != "" {
		dept.Status = status
	}

	// 查询部门列表
	depts, err := c.deptService.SelectDeptList(dept)
	if err != nil {
		c.ErrorJSON(ctx, "查询部门列表失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, common.SuccessDataResponse(depts))
}

// ExcludeChild 查询部门列表（排除节点）
// @Router /system/dept/list/exclude/{deptId} [get]
func (c *SysDeptController) ExcludeChild(ctx *gin.Context) {
	deptId := ctx.Param("deptId")
	id, _ := strconv.ParseInt(deptId, 10, 64)

	// 查询部门列表
	depts, err := c.deptService.SelectDeptList(&model.SysDept{})
	if err != nil {
		c.ErrorJSON(ctx, "查询部门列表失败: "+err.Error())
		return
	}

	// 排除指定节点及其子节点
	filteredDepts := make([]*model.SysDept, 0, len(depts))
	for _, d := range depts {
		// 排除自身
		if d.DeptID == id {
			continue
		}

		// 排除祖先包含当前部门的节点
		if d.Ancestors != "" {
			ancestors := strings.Split(d.Ancestors, ",")
			exclude := false
			for _, a := range ancestors {
				aID, _ := strconv.ParseInt(a, 10, 64)
				if aID == id {
					exclude = true
					break
				}
			}
			if exclude {
				continue
			}
		}

		filteredDepts = append(filteredDepts, d)
	}

	c.SuccessJSON(ctx, common.SuccessDataResponse(filteredDepts))
}

// GetInfo 根据部门编号获取详细信息
// @Router /system/dept/{deptId} [get]
func (c *SysDeptController) GetInfo(ctx *gin.Context) {
	deptId := ctx.Param("deptId")
	id, _ := strconv.ParseInt(deptId, 10, 64)

	// 校验部门是否有数据权限
	err := c.deptService.CheckDeptDataScope(id)
	if err != nil {
		c.ErrorJSON(ctx, err.Error())
		return
	}

	// 查询部门信息
	dept, err := c.deptService.SelectDeptById(id)
	if err != nil {
		c.ErrorJSON(ctx, "查询部门信息失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, common.SuccessDataResponse(dept))
}

// Add 新增部门
// @Router /system/dept [post]
func (c *SysDeptController) Add(ctx *gin.Context) {
	var dept model.SysDept
	if err := ctx.ShouldBindJSON(&dept); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验部门名称是否唯一
	unique, err := c.deptService.CheckDeptNameUnique(&dept)
	if err != nil {
		c.ErrorJSON(ctx, "校验部门名称失败: "+err.Error())
		return
	}
	if !unique {
		c.ErrorJSON(ctx, "新增部门'"+dept.DeptName+"'失败，部门名称已存在")
		return
	}

	// 设置创建者
	dept.CreateBy = c.GetUsername(ctx)

	// 新增部门
	result, err := c.deptService.InsertDept(&dept)
	if err != nil {
		c.ErrorJSON(ctx, "新增部门失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, c.ToAjax(result))
}

// Edit 修改部门
// @Router /system/dept [put]
func (c *SysDeptController) Edit(ctx *gin.Context) {
	var dept model.SysDept
	if err := ctx.ShouldBindJSON(&dept); err != nil {
		c.ErrorJSON(ctx, "参数绑定失败: "+err.Error())
		return
	}

	// 校验部门是否有数据权限
	err := c.deptService.CheckDeptDataScope(dept.DeptID)
	if err != nil {
		c.ErrorJSON(ctx, err.Error())
		return
	}

	// 校验部门名称是否唯一
	unique, err := c.deptService.CheckDeptNameUnique(&dept)
	if err != nil {
		c.ErrorJSON(ctx, "校验部门名称失败: "+err.Error())
		return
	}
	if !unique {
		c.ErrorJSON(ctx, "修改部门'"+dept.DeptName+"'失败，部门名称已存在")
		return
	}

	// 校验上级部门不能是自己
	if dept.ParentID == dept.DeptID {
		c.ErrorJSON(ctx, "修改部门'"+dept.DeptName+"'失败，上级部门不能是自己")
		return
	}

	// 校验部门状态
	if dept.Status == constants.DEPT_DISABLE {
		count, err := c.deptService.SelectNormalChildrenDeptById(dept.DeptID)
		if err != nil {
			c.ErrorJSON(ctx, "查询子部门状态失败: "+err.Error())
			return
		}
		if count > 0 {
			c.ErrorJSON(ctx, "该部门包含未停用的子部门！")
			return
		}
	}

	// 设置更新者
	dept.UpdateBy = c.GetUsername(ctx)

	// 修改部门
	result, err := c.deptService.UpdateDept(&dept)
	if err != nil {
		c.ErrorJSON(ctx, "修改部门失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, c.ToAjax(result))
}

// Remove 删除部门
// @Router /system/dept/{deptId} [delete]
func (c *SysDeptController) Remove(ctx *gin.Context) {
	deptId := ctx.Param("deptId")
	id, _ := strconv.ParseInt(deptId, 10, 64)

	// 校验是否存在子部门
	hasChild, err := c.deptService.HasChildByDeptId(id)
	if err != nil {
		c.ErrorJSON(ctx, "校验子部门失败: "+err.Error())
		return
	}
	if hasChild {
		c.SuccessJSON(ctx, c.WarnMessage("存在下级部门,不允许删除"))
		return
	}

	// 校验是否存在部门用户
	existUser, err := c.deptService.CheckDeptExistUser(id)
	if err != nil {
		c.ErrorJSON(ctx, "校验部门用户失败: "+err.Error())
		return
	}
	if existUser {
		c.SuccessJSON(ctx, c.WarnMessage("部门存在用户,不允许删除"))
		return
	}

	// 校验部门是否有数据权限
	err = c.deptService.CheckDeptDataScope(id)
	if err != nil {
		c.ErrorJSON(ctx, err.Error())
		return
	}

	// 删除部门
	result, err := c.deptService.DeleteDeptById(id)
	if err != nil {
		c.ErrorJSON(ctx, "删除部门失败: "+err.Error())
		return
	}

	c.SuccessJSON(ctx, c.ToAjax(result))
}
