package repository

import (
	"gorm.io/gorm"
)

// Repository 基础Repository接口
type Repository interface {
	// GetDB 获取数据库连接
	GetDB() *gorm.DB
}

// BaseRepository 基础Repository实现
type BaseRepository struct {
	DB *gorm.DB
}

// NewBaseRepository 创建基础Repository
func NewBaseRepository(db *gorm.DB) *BaseRepository {
	return &BaseRepository{
		DB: db,
	}
}

// GetDB 获取数据库连接
func (r *BaseRepository) GetDB() *gorm.DB {
	return r.DB
}
