package model

// SysJob 定时任务调度表 sys_job
type SysJob struct {
	BaseModel
	// 任务ID
	JobID int64 `json:"jobId" gorm:"column:job_id;primary_key;auto_increment;comment:任务ID"`
	// 任务名称
	JobName string `json:"jobName" gorm:"column:job_name;not null;comment:任务名称" validate:"required,max=64"`
	// 任务组名
	JobGroup string `json:"jobGroup" gorm:"column:job_group;not null;default:'DEFAULT';comment:任务组名"`
	// 调用目标字符串
	InvokeTarget string `json:"invokeTarget" gorm:"column:invoke_target;not null;comment:调用目标字符串" validate:"required,max=500"`
	// cron执行表达式
	CronExpression string `json:"cronExpression" gorm:"column:cron_expression;default:'';comment:cron执行表达式" validate:"required,max=255"`
	// 计划执行错误策略（1立即执行 2执行一次 3放弃执行）
	MisfirePolicy string `json:"misfirePolicy" gorm:"column:misfire_policy;default:'3';comment:计划执行错误策略（1立即执行 2执行一次 3放弃执行）"`
	// 是否并发执行（0允许 1禁止）
	Concurrent string `json:"concurrent" gorm:"column:concurrent;default:'1';comment:是否并发执行（0允许 1禁止）"`
	// 状态（0正常 1暂停）
	Status string `json:"status" gorm:"column:status;default:'0';comment:状态（0正常 1暂停）"`
}

// TableName 设置表名
func (SysJob) TableName() string {
	return "sys_job"
}
