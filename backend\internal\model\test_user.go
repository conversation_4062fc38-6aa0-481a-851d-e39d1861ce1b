package model

import (
	"time"
)

// TestUser 测试用户信息
type TestUser struct {
	// 用户ID
	ID int64 `json:"userId"`
	// 部门ID
	DeptID int64 `json:"deptId"`
	// 用户账号
	UserName string `json:"userName"`
	// 用户昵称
	NickName string `json:"nickName"`
	// 用户邮箱
	Email string `json:"email"`
	// 手机号码
	Phone string `json:"phonenumber"`
	// 用户性别（0女 1男 2未知）
	Sex string `json:"sex"`
	// 头像地址
	Avatar string `json:"avatar"`
	// 密码
	Password string `json:"password"`
	// 帐号状态（0正常 1停用）
	Status string `json:"status"`
	// 删除标志（0代表存在 2代表删除）
	DelFlag string `json:"delFlag"`
	// 最后登录IP
	LoginIP string `json:"loginIp"`
	// 最后登录时间
	LoginDate time.Time `json:"loginDate"`
	// 创建者
	CreateBy string `json:"createBy"`
	// 创建时间
	CreateTime time.Time `json:"createTime"`
	// 更新者
	UpdateBy string `json:"updateBy"`
	// 更新时间
	UpdateTime time.Time `json:"updateTime"`
	// 备注
	Remark string `json:"remark"`
}
