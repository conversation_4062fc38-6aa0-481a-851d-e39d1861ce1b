package system

import (
	"backend/internal/api/controller"
	"backend/internal/model"
	"backend/internal/service"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// SysConfigController 参数配置控制器
type SysConfigController struct {
	controller.BaseController
	configService service.SysConfigService
}

// NewSysConfigController 创建参数配置控制器
func NewSysConfigController(configService service.SysConfigService) *SysConfigController {
	return &SysConfigController{
		configService: configService,
	}
}

// List 获取参数配置列表
// @Router /system/config/list [get]
func (c *SysConfigController) List(ctx *gin.Context) {
	// 获取查询参数
	var param model.SysConfig
	pageNum, _ := strconv.Atoi(ctx.DefaultQuery("pageNum", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	configName := ctx.Query("configName")
	configKey := ctx.Query("configKey")
	configType := ctx.Query("configType")

	if configName != "" {
		param.ConfigName = configName
	}
	if configKey != "" {
		param.ConfigKey = configKey
	}
	if configType != "" {
		param.ConfigType = configType
	}

	// 查询参数配置列表
	list, err := c.configService.SelectConfigList(&param)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	// TODO: 实现分页
	// 返回数据
	c.Success(ctx, map[string]interface{}{
		"rows":     list,
		"total":    len(list),
		"pageNum":  pageNum,
		"pageSize": pageSize,
	})
}

// GetInfo 根据参数编号获取详细信息
// @Router /system/config/{configId} [get]
func (c *SysConfigController) GetInfo(ctx *gin.Context) {
	configId, err := strconv.ParseInt(ctx.Param("configId"), 10, 64)
	if err != nil {
		c.Warn(ctx, "参数错误")
		return
	}

	config, err := c.configService.SelectConfigById(configId)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, config)
}

// GetConfigKey 根据参数键名查询参数值
// @Router /system/config/configKey/{configKey} [get]
func (c *SysConfigController) GetConfigKey(ctx *gin.Context) {
	configKey := ctx.Param("configKey")

	configValue, err := c.configService.SelectConfigByKey(configKey)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, configValue)
}

// Add 新增参数配置
// @Router /system/config [post]
func (c *SysConfigController) Add(ctx *gin.Context) {
	var config model.SysConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验参数键名是否唯一
	if !c.configService.CheckConfigKeyUnique(&config) {
		c.Warn(ctx, "新增参数'"+config.ConfigName+"'失败，参数键名已存在")
		return
	}

	// 设置创建者
	config.CreateBy = c.GetUsername(ctx)

	result, err := c.configService.InsertConfig(&config)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, int64(result), nil)
}

// Edit 修改参数配置
// @Router /system/config [put]
func (c *SysConfigController) Edit(ctx *gin.Context) {
	var config model.SysConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		c.Error(ctx, err)
		return
	}

	// 校验参数键名是否唯一
	if !c.configService.CheckConfigKeyUnique(&config) {
		c.Warn(ctx, "修改参数'"+config.ConfigName+"'失败，参数键名已存在")
		return
	}

	// 设置更新者
	config.UpdateBy = c.GetUsername(ctx)

	result, err := c.configService.UpdateConfig(&config)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.ToAjax(ctx, int64(result), nil)
}

// Remove 删除参数配置
// @Router /system/config/{configIds} [delete]
func (c *SysConfigController) Remove(ctx *gin.Context) {
	configIdsStr := ctx.Param("configIds")
	configIds := make([]int64, 0)

	for _, idStr := range strings.Split(configIdsStr, ",") {
		if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
			configIds = append(configIds, id)
		}
	}

	err := c.configService.DeleteConfigByIds(configIds)
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// RefreshCache 刷新参数缓存
// @Router /system/config/refreshCache [delete]
func (c *SysConfigController) RefreshCache(ctx *gin.Context) {
	err := c.configService.ResetConfigCache()
	if err != nil {
		c.Error(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// RegisterRoutes 注册路由
func (c *SysConfigController) RegisterRoutes(router *gin.RouterGroup) {
	configRouter := router.Group("/system/config")
	{
		configRouter.GET("/list", c.List)
		configRouter.GET("/:configId", c.GetInfo)
		configRouter.GET("/configKey/:configKey", c.GetConfigKey)
		configRouter.POST("", c.Add)
		configRouter.PUT("", c.Edit)
		configRouter.DELETE("/:configIds", c.Remove)
		configRouter.DELETE("/refreshCache", c.RefreshCache)
	}
}
